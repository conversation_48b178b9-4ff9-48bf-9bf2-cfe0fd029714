#!/usr/bin/env python
# encoding:utf-8

import argparse  # 导入参数处理库
from loguru import logger
import time
import os
import json
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# --- 全局配置区域 ---
LOGIN_USERNAME_XPATH = "//input[@name='username']"
LOGIN_PASSWORD_XPATH = "//input[@name='password']"
LOGIN_BUTTON_XPATH = "//input[@id='submit_login']"

CONTENT_IFRAME_XPATH = "//div[@id='prefecture_City_linkage']/iframe"
TASK_XPATH = "//*[@id='tab-2']"
CONTACT_XPATH = "//*[@id='tab-3']"

# 工作通知&预警通告
NOTICE_TABLE_BODY_XPATH = "//table[contains(@class, 'el-table__body')]/tbody"
PROCESSED_ICON_XPATH = ".//td[6]//i[contains(@class, 'el-icon-success')]"
VIEW_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '查看')]]"
DOWNLOAD_BUTTON_XPATH = ".//td[7]//button[span[contains(text(), '下载')]]"
TITLE_XPATH = ".//td[3]//div[contains(@class, 'newsTitle')]"
NEXT_PAGE_BUTTON_XPATH = "//button[contains(@class, 'btn-next') and not(@disabled)]"

ATTACHMENT_INDICATOR_XPATH = "//*[@id='viewDialog']/div/div/div[2]/form/div[5]/div/div/label" 

# 预警通告不同的XPATH
YJTG_IFRAME_XPATH = "//div[@id='ws-announce-jump_area']/iframe" 
YJTG_TITLE_XPATH = ".//td[2]//div[contains(@class, 'newsTitle')]"
YJTG_CONTENT_XPATH = "//*[@id='renderContentHtml']/p"

# 工作任务&工作联系单
TASK_STATUS_TEXT_XPATH = ".//td[7]/div"
TASK_VIEW_BUTTON_XPATH = ".//td[8]//button[span[contains(text(), '查看')]]"
TASK_DOWNLOAD_BUTTON_XPATH = ".//td[8]//button[span[contains(text(), '下载')]]"

# --- 菜单导航 XPath (基于 page_source1.html) ---
MENU_YJXY_XPATH = "//li[div/span[contains(text(), '应急响应')]]"
MENU_QSYJ_XPATH = "//li[div/span[contains(text(), '全省预警')]]"
MENU_ZGDGL_XPATH = "//li[span[contains(text(), '预警单反馈')]]" # 预警单管理
MENU_QSYJTG_XPATH = "//li[div/span[contains(text(), '全省预警通告')]]"
MENU_DSYJTG_XPATH = "//li[span[contains(text(), '地市预警通告')]]" # 预警通告管理

# --- 预警单页面 XPath ---
YJD_IFRAME_XPATH = "//div[@id='area_vulnfeedback']/iframe" 
YJD_TABLE_BODY_XPATH = "//table[contains(@class, 'el-table__body')]/tbody"
YJD_TITLE_XPATH = ".//td[3]//span"
YJD_STATUS_TEXT_XPATH = ".//td[11]//span"
YJD_VIEW_BUTTON_XPATH = ".//td[14]//button[contains(span, '查看')]"
# 弹窗内的元素
POPUP_TEXTAREA_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[6]/div/div/div/div/textarea"
POPUP_ATTACHMENT_LINK_XPATH = "//*[@id='earlyAlarm']/div[3]/div/div/div[2]/form/div[9]/div/div/div/div/a"

S6000_USERNAME = "q1"
S6000_PASSWORD = "H1"
S6000_URL = "http://10.11.11.11:28088/Portal/mainAdmin/main"


# --- 任务函数定义 ---

def login(driver, wait, url, username, password):
    """负责登录S6000系统，并验证是否成功。"""
    try:
        logger.info(f"[S6000系统] 访问登录页面: {url}")
        driver.get(url)
        logger.info("[S6000系统] 输入凭据并执行登录...")
        wait.until(EC.visibility_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH))).send_keys(username)
        driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH).send_keys(password)
        driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH).click()
        logger.info("[S6000系统] 验证登录结果...")
        wait.until(EC.presence_of_element_located((By.XPATH, CONTENT_IFRAME_XPATH)))
        logger.info("[S6000系统] 登录成功，页面加载完成。")
        return True
    except Exception as e:
        logger.error(f"[S6000系统] 登录过程中发生错误: {e}", exc_info=True)
        driver.save_screenshot('error_login_failed.png')
        return False
        
def navigate_to_warning_notices(driver, wait, k):
    """导航到“预警单管理”或“地市预警通告”页面。"""
    try:
        logger.info("--- 开始页面导航 ---")
        actions = ActionChains(driver)

        logger.info("导航步骤1: 悬停在'应急响应'菜单...")
        emergency_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_YJXY_XPATH)))
        actions.move_to_element(emergency_menu).perform()
        time.sleep(0.5)
        
        if k == 4:
            logger.info("导航步骤2: 悬停在'全省预警'子菜单...")
            warning_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_QSYJ_XPATH)))
            actions.move_to_element(warning_menu).perform()
            time.sleep(0.5)

            logger.info("导航步骤3: 点击'预警单管理'...")
            rectification_item = wait.until(EC.element_to_be_clickable((By.XPATH, MENU_ZGDGL_XPATH)))
            rectification_item.click()
        
            logger.info("已成功导航到'预警单管理'页面。")
            return True
        elif k == 5:
            logger.info("导航步骤2: 悬停在'全省预警通告'子菜单...")
            warning_menu = wait.until(EC.visibility_of_element_located((By.XPATH, MENU_QSYJTG_XPATH)))
            actions.move_to_element(warning_menu).perform()
            time.sleep(0.5)

            logger.info("导航步骤3: 点击'地市预警通告'...")
            rectification_item = wait.until(EC.element_to_be_clickable((By.XPATH, MENU_DSYJTG_XPATH)))
            rectification_item.click()
        
            logger.info("已成功导航到'地市预警通告'页面。")
            return True
        else:
            logger.error(f"导航至'预警单管理'或'地市预警通告'时出错，参数k为'{k}'，请使用4（预警单）或5（地市预警通告）")
            return False
    except Exception as e:
        logger.error(f"导航至'预警单管理'或'地市预警通告'时出错: {e}", exc_info=True)
        return False

def wait_for_download_complete(directory, timeout=300):
    """
    等待指定目录中的所有.crdownload文件消失（即所有下载完成）。
    """
    logger.info(f"开始最终下载监控，最长等待 {timeout} 秒...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        # 检查目录中是否还存在.crdownload临时文件
        if not any(filename.endswith('.crdownload') for filename in os.listdir(directory)):
            time.sleep(2) # 额外等待2秒，确保文件系统完全同步
            logger.info("监控到所有下载已完成！")
            return True
        time.sleep(1) # 每秒检查一次
    
    logger.error("下载超时！在规定时间内有文件未下载完成。")
    return False

def process_work_notices(driver, wait):
    """【任务一：工作通知】处理所有“未处理”的工作通知。"""
    processed_notices = []
    download_count = 0
    try:
        logger.info("--- 开始处理'工作通知'模块 ---")
        # 1. 进入工作区域的IFrame
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        logger.info("已进入'工作通知'的IFrame。")
        page_num = 1
        # 测试1line
        #test_num = 0
        while True:
            logger.info(f"开始处理第 {page_num} 页...")
            time.sleep(1)
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    
                    # 尝试寻找已处理图标，如果找到，则跳过
                    current_row.find_element(By.XPATH, PROCESSED_ICON_XPATH)
                    logger.info(f"在第 {i+1} 行发现已处理通知，默认后续通知均已处理。")
                    return {"task": "工作通知", "download_started": download_count, "processed_count": len(processed_notices), "processed_titles": processed_notices}
                    # 测试时注释上下两行，使用三条数据
                except NoSuchElementException:
                    # 找不到图标，说明是“未处理”项，执行核心操作
                    title = current_row.find_element(By.XPATH, TITLE_XPATH).text.strip()
                    logger.info(f"发现未处理通知: '{title}'，开始处理...")
                    
                    # 智能判断并下载
                    current_row.find_element(By.XPATH, VIEW_BUTTON_XPATH).click()
                    time.sleep(1)

                    has_attachment = False
                    try:
                        driver.find_element(By.XPATH, ATTACHMENT_INDICATOR_XPATH)
                        has_attachment = True
                        logger.info("  -> 判断结果: 有附件。")
                    except NoSuchElementException:
                        logger.info("  -> 判断结果: 无附件。")
                    
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    time.sleep(1)

                    if has_attachment:
                        logger.info("  -> 执行下载...")
                        current_row.find_element(By.XPATH, DOWNLOAD_BUTTON_XPATH).click()
                        download_count += 1 
                        time.sleep(2)
                    
                    processed_notices.append(title)
                    logger.info(f"通知 '{title}' 处理完成。")
                # 测试4lines
                #except NoSuchElementException:
                #    continue
                #if test_num == 3:
                #    return {"task": "工作通知", "download_started": download_count, "processed_count": len(processed_notices), "processed_titles": processed_notices}
            # 如果for循环正常结束，说明本页均为“未处理”项，需要翻页检查
            logger.info(f"第 {page_num} 页已无未处理项。")
            try:
                logger.info("尝试翻页...")
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
                page_num += 1
            except NoSuchElementException:
                logger.info("已是最后一页，工作通知处理完毕。")
                break # 退出while循环

    except Exception as e:
        logger.error(f"处理工作通知时发生错误: {e}", exc_info=True)
    finally:
        # 确保退出时，将驱动焦点切回主页面
        driver.switch_to.default_content()
        logger.info("--- '工作通知'模块处理结束 ---")
    
    return {"task": "工作通知", "download_started": download_count, "processed_count": len(processed_notices), "processed_titles": processed_notices}

def process_work_tasks(driver, wait):
    """【任务二：工作任务】"""
    processed_tasks = []
    download_count = 0
    try:
        logger.info("--- 开始处理'工作任务'模块 ---")
        # 1. 进入工作区域的IFrame
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        logger.info("已进入'工作任务'的IFrame。")
        driver.find_element(By.XPATH, TASK_XPATH).click()
        time.sleep(3)
        logger.info("[S6000系统] 已成功切换到工作任务")

        page_num = 1
        while True:
            logger.info(f"开始处理第 {page_num} 页...")
            time.sleep(1)
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    
                    # 尝试寻找已反馈文本，如果找到，则跳过
                    status_text = current_row.find_element(By.XPATH, TASK_STATUS_TEXT_XPATH).text.strip()
                    
                    if status_text == "未反馈":
                        title = current_row.find_element(By.XPATH, TITLE_XPATH).text.strip()
                        logger.info(f" 发现未反馈任务: '{title}'，开始操作...")
                        
                        current_row.find_element(By.XPATH, TASK_VIEW_BUTTON_XPATH).click()
                        time.sleep(1)
                        ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                        time.sleep(1)

                        logger.info("  -> 执行下载...")
                        current_row.find_element(By.XPATH, TASK_DOWNLOAD_BUTTON_XPATH).click()
                        download_count += 1
                        time.sleep(2)
                        
                        processed_tasks.append(title)
                        logger.info(f" 工作任务 '{title}'完成 ")   
                        
                    else:
                        logger.info(f"在第 {i+1} 行发现已反馈任务，默认后续任务均已反馈。")
                        return {"task": "工作任务", "download_started": download_count, "processed_count": len(processed_tasks), "processed_titles": processed_tasks}
                
                except NoSuchElementException:
                    continue
            
            # 如果for循环正常结束，说明本页均为“未反馈”项，需要翻页检查
            logger.info(f"第 {page_num} 页已无未反馈项。")
            try:
                logger.info("尝试翻页...")
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
                page_num += 1
            except NoSuchElementException:
                logger.info("已是最后一页，工作任务处理完毕。")
                break # 退出while循环

    except Exception as e:
        logger.error(f"处理工作任务时发生错误: {e}", exc_info=True)
    finally:
        # 确保退出时，将驱动焦点切回主页面
        driver.switch_to.default_content()
        logger.info("--- '工作任务'模块处理结束 ---")
    
    return {"task": "工作任务", "download_started": download_count, "processed_count": len(processed_tasks), "processed_titles": processed_tasks}

def process_work_contact_sheets(driver, wait):
    """【任务三：工作联系单】"""
    logger.info("--- 开始执行【工作联系单】处理模块 ---")
    processed_contact_sheets = []
    download_count = 0
    try:
        logger.info("--- 开始处理'工作联系单'模块 ---")
        # 1. 进入工作区域的IFrame
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, CONTENT_IFRAME_XPATH)))
        logger.info("已进入'工作联系单'的IFrame。")
        driver.find_element(By.XPATH, CONTACT_XPATH).click()
        time.sleep(3)
        logger.info("[S6000系统] 已成功切换到工作联系单")

        page_num = 1
        while True:
            logger.info(f"开始处理第 {page_num} 页...")
            time.sleep(1)
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    
                    # 尝试寻找已反馈文本，如果找到，则跳过
                    status_text = current_row.find_element(By.XPATH, TASK_STATUS_TEXT_XPATH).text.strip()
                    
                    if status_text == "未反馈":
                        title = current_row.find_element(By.XPATH, TITLE_XPATH).text.strip()
                        logger.info(f" 发现未反馈任务: '{title}'，开始操作...")
                        
                        current_row.find_element(By.XPATH, TASK_VIEW_BUTTON_XPATH).click()
                        time.sleep(1)
                        ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                        time.sleep(1)

                        logger.info("  -> 执行下载...")
                        current_row.find_element(By.XPATH, TASK_DOWNLOAD_BUTTON_XPATH).click()
                        download_count += 1
                        time.sleep(2)
                        
                        processed_contact_sheets.append(title)
                        logger.info(f" 工作联系单 '{title}'完成 ")   
                        
                    else:
                        logger.info(f"在第 {i+1} 行发现已反馈联系单，默认后续联系单均已反馈。")
                        return {"task": "工作联系单", "download_started": download_count, "processed_count": len(processed_contact_sheets), "processed_titles": processed_contact_sheets}
                
                except NoSuchElementException:
                    continue
            
            # 如果for循环正常结束，说明本页均为“未反馈”项，需要翻页检查
            logger.info(f"第 {page_num} 页已无未反馈项。")
            try:
                logger.info("尝试翻页...")
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
                page_num += 1
            except NoSuchElementException:
                logger.info("已是最后一页，工作联系单处理完毕。")
                break # 退出while循环

    except Exception as e:
        logger.error(f"处理工作联系单时发生错误: {e}", exc_info=True)
    finally:
        # 确保退出时，将驱动焦点切回主页面
        driver.switch_to.default_content()
        logger.info("--- '工作联系单'模块处理结束 ---")
    
    return {"task": "工作联系单", "download_started": download_count, "processed_count": len(processed_contact_sheets), "processed_titles": processed_contact_sheets}

def process_task_warning_notices(driver, wait):
    """【任务四：预警单管理】"""
    processed_orders = []
    download_count = 0
    if navigate_to_warning_notices(driver, wait, k=4) is False:
        logger.error("导航失败，无法执行后续任务。")
        return {"task": "预警单管理 ", "download_started": download_count, "processed_count": len(processed_orders), "processed_titles": processed_orders}
    
    try:
        logger.info("--- 开始执行【预警单管理】模块 ---")

        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, YJD_IFRAME_XPATH)))
        logger.info("已进入内容IFrame。")

        logger.info(f"开始扫描页面以寻找'已完成'的预警单 ...")
        time.sleep(2) # 等待列表加载
        wait.until(EC.presence_of_element_located((By.XPATH, YJD_TABLE_BODY_XPATH)))
        rows = driver.find_elements(By.XPATH, f"{YJD_TABLE_BODY_XPATH}/tr")

        for i in range(len(rows)):
            
            try:
                current_row = driver.find_element(By.XPATH, f"({YJD_TABLE_BODY_XPATH}/tr)[{i+1}]")
                title_name = current_row.find_element(By.XPATH, YJD_TITLE_XPATH).text.strip()
                status_element = current_row.find_element(By.XPATH, YJD_STATUS_TEXT_XPATH)
                
                if status_element.text.strip() == "已完成":
                    logger.info(f"[模拟处理] 找到第 {i+1} 行待处理的预警单： '{title_name}'，开始操作...")
                    
                    # 1. 点击查看
                    current_row.find_element(By.XPATH, YJD_VIEW_BUTTON_XPATH).click()
                    
                    # 2. 等待弹窗并获取情报内容
                    title_element = wait.until(EC.visibility_of_element_located((By.XPATH, POPUP_TEXTAREA_XPATH)))
                    time.sleep(1)
                    intelligence_content = title_element.get_attribute('value')
                    time.sleep(1)
                    logger.info(f"  -> 获取情报内容: {intelligence_content}")
                    
                    # 3. 点击附件链接下载
                    try:
                        attachment_link = wait.until(EC.element_to_be_clickable((By.XPATH, POPUP_ATTACHMENT_LINK_XPATH)))
                        logger.info(f"  -> 点击附件下载: {attachment_link.text}")
                        attachment_link.click()
                        download_count += 1
                        time.sleep(2) # 等待下载触发
                    except TimeoutException:
                        logger.warning("  -> 未在该弹窗中找到附件链接。")

                    # 4. 关闭弹窗
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    logger.info("  -> 关闭弹窗。")
                    time.sleep(1)

                    processed_orders.append(intelligence_content)
                    logger.info(f" 第 {i+1} 行预警单处理完成 ")
                
                else:
                    logger.info(f"在第 {i+1} 行发现已完成预警单，默认后续预警单均已完成")
                    return {"task": "预警单管理 ", "download_started": download_count, "processed_count": len(processed_orders), "processed_titles": processed_orders}

            except Exception as e:
                logger.error(f"处理第 {i+1} 行预警单时发生未知错误: {e}", exc_info=True)
    
    except Exception as e:
        logger.error(f"处理预警单时发生错误: {e}", exc_info=True)
    finally:
        driver.switch_to.default_content()
        logger.info("--- 【预警单管理】模块处理结束 ---")
    
    return {"task": "预警单管理 ", "download_started": download_count, "processed_count": len(processed_orders), "processed_titles": processed_orders}


def process_task_alert_announcement(driver, wait):
    """【任务五：地市预警通告】"""
    processed_alerta = []
    download_count = 0
    if navigate_to_warning_notices(driver, wait, k=5) is False:
        logger.error("导航失败，无法执行后续任务。")
        return {"task": "地市预警通告 ", "download_started": download_count, "processed_count": len(processed_alerta), "processed_titles": processed_alerta}
    
    try:
        logger.info("--- 开始处理'地市预警通告'模块 ---")
        # 1. 进入工作区域的IFrame
        wait.until(EC.frame_to_be_available_and_switch_to_it((By.XPATH, YJTG_IFRAME_XPATH)))
        logger.info("已进入'地市预警通告'的IFrame。")

        page_num = 1
        while True:
            logger.info(f"开始处理第 {page_num} 页...")
            time.sleep(1)
            wait.until(EC.presence_of_element_located((By.XPATH, NOTICE_TABLE_BODY_XPATH)))
            rows = driver.find_elements(By.XPATH, f"{NOTICE_TABLE_BODY_XPATH}/tr")

            for i in range(len(rows)):
                try:
                    current_row = driver.find_element(By.XPATH, f"({NOTICE_TABLE_BODY_XPATH}/tr)[{i+1}]")
                    
                    # 尝试寻找已处理图标，如果找到，则跳过
                    current_row.find_element(By.XPATH, PROCESSED_ICON_XPATH)
                    logger.info(f"在第 {i+1} 行发现已处理通告，默认后续通告均已处理。")
                    return {"task": "地市预警通告", "download_started": download_count, "processed_count": len(processed_alerta), "processed_titles": processed_alerta}
                
                except NoSuchElementException:
                    # 找不到图标，说明是“未处理”项，执行核心操作
                    title = current_row.find_element(By.XPATH, YJTG_TITLE_XPATH).text.strip()
                    logger.info(f"发现未处理通告: '{title}'，开始处理...")
                    
                    # 1. 点击“查看”
                    current_row.find_element(By.XPATH, VIEW_BUTTON_XPATH).click()
                    logger.info("  -> 步骤1: 打开'查看'弹窗...")
                    time.sleep(1) 
                    
                    # 2. 获取正文
                    text_content = current_row.find_element(By.XPATH, YJTG_CONTENT_XPATH).text.strip()
                    logger.info("  -> 步骤2: 获取正文内容...")
                    time.sleep(1) 

                    # 3. 关闭弹窗
                    ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                    logger.info("  -> 步骤3: 关闭'查看'弹窗。")
                    time.sleep(1)

                    logger.info("  -> 步骤4: 执行下载操作...")
                    current_row.find_element(By.XPATH, DOWNLOAD_BUTTON_XPATH).click()
                    download_count += 1
                    time.sleep(2) # 等待下载触发

                    processed_alerta.append(text_content)
                    logger.info(f"通告'{title}'处理完成")
            
            # 如果for循环正常结束，说明本页均为“未处理”项，需要翻页检查
            logger.info(f"第 {page_num} 页已无未处理项。")
            try:
                logger.info("尝试翻页...")
                driver.find_element(By.XPATH, NEXT_PAGE_BUTTON_XPATH).click()
                page_num += 1
            except NoSuchElementException:
                logger.info("已是最后一页，地市预警通告处理完毕。")
                break # 退出while循环

    except Exception as e:
        logger.error(f"处理地市预警通告时发生错误: {e}", exc_info=True)
    finally:
        # 确保退出时，将驱动焦点切回主页面
        driver.switch_to.default_content()
        logger.info("--- '地市预警通告'模块处理结束 ---")
    
    return {"task": "地市预警通告", "download_started": download_count, "processed_count": len(processed_alerta), "processed_titles": processed_alerta}

# --- 主程序入口 ---

if __name__ == '__main__':
    # 1. 配置命令行参数解析器
    parser = argparse.ArgumentParser(
        description="S6000 自动化任务机器人",
        formatter_class=argparse.RawTextHelpFormatter
    )
    # 互斥组，一次仅执行一个任务
    task_group = parser.add_mutually_exclusive_group(required=True)
    # 每个任务提供长短两种格式的参数
    task_group.add_argument('-n', '--notice', action='store_true', help='执行"工作通知"处理任务')
    task_group.add_argument('-t', '--task', action='store_true', help='执行"工作任务"处理任务')
    task_group.add_argument('-c', '--contact_sheet', action='store_true', help='执行"工作联系单"处理任务')
    task_group.add_argument('-w', '--warning_notice', action='store_true', help='执行"预警单管理"处理任务')
    task_group.add_argument('-a', '--alert_announcement', action='store_true', help='执行"地市预警通告"处理任务')
    
    args = parser.parse_args()
    
    # 任务名称与日志名称映射
    task_name = "unknown"
    if args.notice: task_name = "notice"
    elif args.task: task_name = "task"
    elif args.contact_sheet: task_name = "contact_sheet"
    elif args.warning_notice: task_name = "warning_notice"
    elif args.alert_announcement: task_name = "alert_announcement"

    # 1. 设置下载目录、监控下载
    download_dir = os.path.join(os.getcwd(), r"C:\Users\<USER>\Desktop\AM_AUTO\download")
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    logger.info(f"所有文件将被下载到: {download_dir}")

    # 2. 配置Loguru日志
    logger.remove()
    logger.add(lambda msg: print(msg, end=''), level="INFO")
    logger.add(f"s6000_robot_{task_name}.log", level="DEBUG", rotation="10 MB", encoding="utf-8")
    
    # 3. WebDriver初始化和任务调度
    driver = None
    results = None
    try:
        chrome_options = Options()
        chrome_options.binary_location = r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--log-level=3')
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
        }
        
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        chrome_options.add_experimental_option("prefs", prefs)
        
        service = Service(log_output='selenium_driver.log')
        logger.info("[S6000系统] 初始化WebDriver")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # --- 为无头模式启用下载 ---
        # 发送CDP指令，明确允许下载行为并指定路径
        driver.execute_cdp_cmd(
            "Page.setDownloadBehavior",
            {
                "behavior": "allow",      # 允许所有下载
                "downloadPath": download_dir # 指定下载路径
            }
        )
        logger.info("已为无头模式配置下载许可。")
        # ------------------------------------

        wait = WebDriverWait(driver, 10)

        # 执行登录
        if login(driver, wait, S6000_URL, S6000_USERNAME, S6000_PASSWORD):
            
            results = None
            # --- 任务分发器 ---
            if args.notice:
                results = process_work_notices(driver, wait)
            elif args.task:
                results = process_work_tasks(driver, wait)
            elif args.contact_sheet:
                results = process_work_contact_sheets(driver, wait)
            elif args.warning_notice:
                results = process_task_warning_notices(driver, wait)
            elif args.alert_announcement:
                results = process_task_alert_announcement(driver, wait)
            
            # 保存结果到JSON文件
            result_file = f"s6000_result_{task_name}.json"
            if results:
                output_data = {
                    "task_name": task_name,
                    "processed_count": results.get("processed_count", []),
                    "processed_titles": results.get("processed_titles", []),
                    "download_directory": download_dir,
                    "success": True,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                logger.info(f"任务 '{task_name}' 执行完毕，最终结果: {results}")
            else:
                output_data = {
                    "success": False, 
                    "error": "任务执行失败",
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
            
            # 写入结果文件
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到: {result_file}")
            
            # 输出结果文件路径供其他脚本使用
            print(f"RESULT_FILE:{result_file}")
            
        else:
            logger.error("登录失败，程序终止。")
            # 登录失败也保存结果
            output_data = {
                "success": False, 
                "error": "登录失败",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            result_file = f"s6000_result_{task_name}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            print(f"RESULT_FILE:{result_file}")

    except Exception as e:
        logger.error(f"主程序发生致命错误: {e}", exc_info=True)
        if driver:
            driver.save_screenshot('error_main_fatal.png')
        
        # 异常情况也保存结果
        output_data = {
            "success": False, 
            "error": str(e),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        result_file = f"s6000_result_{task_name}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        print(f"RESULT_FILE:{result_file}")
        
    finally:
        # --- 在退出前统一等待下载 ---
        if driver and results and results.get("downloads_started", 0) > 0:
            wait_for_download_complete(download_dir)
            
        if driver:
            time.sleep(5)
            logger.info("所有任务完成，关闭WebDriver。")
            driver.quit()
