# 天眼告警查询APP使用示例

## 在SOAR工作流中的使用

### 1. 基本配置

在SOAR工作流中添加"天眼告警查询"节点，配置以下参数：

```json
{
  "base_url": "https://*************",
  "username": "admin", 
  "password": "your_password",
  "hours_ago": 1,
  "limit": 50
}
```

### 2. 返回数据格式

成功执行后，返回的数据格式如下：

```json
{
  "status": 0,
  "result": {
    "total": 3,
    "alerts": [
      {
        "access_time": "7月21日11:30",
        "alarm_sip": "*************", 
        "table_name": "网页漏洞利用",
        "rule_name": "SQL注入攻击检测"
      },
      {
        "access_time": "7月21日11:25",
        "alarm_sip": "*********",
        "table_name": "网络攻击", 
        "rule_name": "端口扫描检测"
      },
      {
        "access_time": "7月21日11:20",
        "alarm_sip": "************",
        "table_name": "其他攻击",
        "rule_name": "异常流量检测"
      }
    ]
  }
}
```

### 3. 工作流集成示例

#### 场景1：定时告警检查
```
[定时器] -> [天眼告警查询] -> [条件判断] -> [邮件通知]
                                |
                                -> [钉钉通知]
```

#### 场景2：告警处理流程
```
[天眼告警查询] -> [循环处理] -> [IP查询] -> [威胁情报查询] -> [自动封禁]
```

#### 场景3：告警统计报告
```
[天眼告警查询] -> [数据处理] -> [生成报告] -> [邮件发送]
```

### 4. 数据处理示例

在后续节点中可以这样处理返回的数据：

```python
# 获取告警总数
total_alerts = result["result"]["total"]

# 遍历所有告警
for alert in result["result"]["alerts"]:
    access_time = alert["access_time"]  # 监测时间
    victim_ip = alert["alarm_sip"]      # 受害者IP
    attack_type = alert["table_name"]   # 告警类型
    threat_name = alert["rule_name"]    # 威胁名称
    
    # 根据告警类型进行不同处理
    if attack_type == "网页漏洞利用":
        # 处理Web攻击
        pass
    elif attack_type == "网络攻击":
        # 处理网络攻击
        pass
```

### 5. 错误处理

当返回status为2时，表示执行失败：

```json
{
  "status": 2,
  "result": "登录天眼平台失败，请检查地址、用户名和密码"
}
```

常见错误及解决方案：

- **登录失败**: 检查base_url、username、password是否正确
- **网络连接失败**: 检查网络连通性和防火墙设置
- **权限不足**: 确认账户有查询告警的权限
- **依赖模块缺失**: 安装required依赖 `pip install requests ddddocr`

### 6. 最佳实践

1. **定时查询**: 建议设置定时任务，每小时查询一次
2. **数量限制**: 根据系统性能调整limit参数，避免一次查询过多数据
3. **错误重试**: 在工作流中添加重试机制，处理网络波动
4. **数据存储**: 将查询结果存储到数据库，便于历史分析
5. **告警去重**: 对相同IP和威胁类型的告警进行去重处理

### 7. 与其他APP的联动

- **IP查询APP**: 查询受害者IP的详细信息
- **邮件APP**: 发送告警通知邮件
- **钉钉APP**: 发送即时通知
- **MySQL APP**: 将告警数据存储到数据库
- **网络设备APP**: 自动封禁攻击源IP
