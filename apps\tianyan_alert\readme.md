# 天眼告警查询 APP

## APP 说明

> 奇安信天眼告警检测平台API工具，用于查询过去5分钟内的安全告警信息

## 功能特性

- 自动登录天眼平台（支持验证码识别）
- 查询过去5分钟内的告警信息
- 自动格式化告警数据，包含8个关键字段
- 支持告警类型中文映射
- 时间戳自动转换为北京时间格式

## 动作列表

### 查询告警

**参数：**

|  参数   | 类型  |  必填   |  默认值  |  备注  |
|  ----  | ----  |  ----  |  ----  |  ----  |
| **base_url**  | text | `是` | https://************* | 天眼平台地址 |
| **username**  | text | `是` | admin | 登录用户名 |
| **password**  | text | `是` | - | 登录密码 |
| **minutes_ago**  | number | `否` | 5 | 查询多少分钟前的告警 |
| **limit**  | number | `否` | 50 | 返回告警数量限制 |

**返回值格式：**

```json
{
  "status": 0,
  "result": {
    "total": 10,
    "alerts": [
      {
        "latest_time": "7月21日11:30",
        "victim_ip": "*************",
        "attack_ip": "*********",
        "alert_type": "网页漏洞利用",
        "threat_name": "SQL注入攻击检测",
        "attack_result": "成功",
        "threat_level": "高危",
        "count": 3
      }
    ]
  }
}
```

**字段说明：**

- `latest_time`: 最近发生时间，格式为"x月x日xx:xx"（北京时间）
- `victim_ip`: 受害IP地址
- `attack_ip`: 攻击IP地址
- `alert_type`: 告警类型（已映射中文）
  - `webids_alert` → `网页漏洞利用`
  - `ips_alert` → `网络攻击`
  - 其他值 → `其他攻击`
- `threat_name`: 威胁名称
- `attack_result`: 攻击结果（成功、企图、失败）
- `threat_level`: 威胁级别（危急、高危、中危、低危）
- `count`: 攻击次数

**错误返回：**

```json
{
  "status": 2,
  "result": "错误信息描述"
}
```

## 依赖要求

- Python 3.6+
- requests
- ddddocr (验证码识别)
- loguru (日志记录)

## 注意事项

1. 确保网络能够访问天眼平台
2. 账户需要有相应的查询权限
3. 验证码识别可能不是100%准确，系统会自动重试
4. 避免过于频繁的请求，以免对系统造成压力
