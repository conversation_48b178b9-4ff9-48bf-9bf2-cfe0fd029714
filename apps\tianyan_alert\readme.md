# 天眼告警查询 APP

## APP 说明

> 奇安信天眼告警检测平台API工具，用于查询过去指定时间内的安全告警信息

## 功能特性

- 自动登录天眼平台（支持验证码识别）
- 查询指定时间范围内的告警信息
- 自动格式化告警数据
- 支持告警类型中文映射
- 时间戳自动转换为北京时间格式

## 动作列表

### 查询告警

**参数：**

|  参数   | 类型  |  必填   |  默认值  |  备注  |
|  ----  | ----  |  ----  |  ----  |  ----  |
| **base_url**  | text | `是` | https://************* | 天眼平台地址 |
| **username**  | text | `是` | admin | 登录用户名 |
| **password**  | text | `是` | - | 登录密码 |
| **hours_ago**  | number | `否` | 1 | 查询多少小时前的告警 |
| **limit**  | number | `否` | 50 | 返回告警数量限制 |

**返回值格式：**

```json
{
  "status": 0,
  "result": {
    "total": 10,
    "alerts": [
      {
        "access_time": "7月21日11:30",
        "alarm_sip": "*************",
        "table_name": "网页漏洞利用",
        "rule_name": "SQL注入攻击检测"
      }
    ]
  }
}
```

**字段说明：**

- `access_time`: 监测时间，格式为"x月x日xx:xx"（北京时间）
- `alarm_sip`: 受害者IP地址
- `table_name`: 告警类型（已映射中文）
  - `webids_alert` → `网页漏洞利用`
  - `ips_alert` → `网络攻击`
  - 其他值 → `其他攻击`
- `rule_name`: 威胁名称

**错误返回：**

```json
{
  "status": 2,
  "result": "错误信息描述"
}
```

## 依赖要求

- Python 3.6+
- requests
- ddddocr (验证码识别)
- loguru (日志记录)

## 注意事项

1. 确保网络能够访问天眼平台
2. 账户需要有相应的查询权限
3. 验证码识别可能不是100%准确，系统会自动重试
4. 避免过于频繁的请求，以免对系统造成压力
