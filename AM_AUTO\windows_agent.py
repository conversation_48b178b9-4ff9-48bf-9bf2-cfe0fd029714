# windows_agent.py (最终版 - 实时输出 & 固定路径)

from flask import Flask, jsonify, request
import subprocess
import threading
import os
import json
import time

# 初始化 Flask 应用
app = Flask(__name__)

# --- 配置区 ---
S6000_SCRIPT_PATH = "C:\\Users\\<USER>\\Desktop\\AM_AUTO\\s6000_selenium.py"
RPA_SCRIPT_PATH = "C:\\Users\\<USER>\\Desktop\\AM_AUTO\\rpa.py"
# PYTHON_EXECUTABLE = "C:\\Python312\\python.exe"

# --- 任务状态管理 ---
task_lock = threading.Lock()
last_task_status = {
    "status": "idle",
    "task_type": None,
    "message": "系统当前无任务",
    "timestamp": None
}

def run_subprocess_with_realtime_output(command, task_name):
    """一个通用的函数，用于执行子进程并实时打印其输出。"""
    print(f"\n--- {task_name} 输出 [START] ---")
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        encoding='gbk',
        errors='replace'
    )
    
    for line in process.stdout:
        print(line, end='', flush=True) # flush=True 保证立即输出

    print(f"--- {task_name} 输出 [END] ---\n")
    
    return_code = process.wait()
    if return_code != 0:
        raise RuntimeError(f"{task_name} 脚本执行失败，返回码: {return_code}")

def run_full_automation_flow(task_type, recipient_list):
    """
    一个完整的、顺序执行的自动化流程。
    """
    global last_task_status
    
    try:
        # --- 核心修改 1: 直接构建S6000结果文件的路径 ---
        s6000_result_file = f"s6000_result_{task_type}.json"
        print(f"[*] 预期的S6000结果文件: {s6000_result_file}")
        
        # --- 阶段 1: 执行 S6000 Selenium 任务 (实时显示输出) ---
        task_flags = {
            'notice': '-n', 'task': '-t', 'contact_sheet': '-c',
            'warning_notice': '-w', 'alert_announcement': '-a'
        }
        cmd_s6000 = ['python', '-u', S6000_SCRIPT_PATH, task_flags[task_type]]
        run_subprocess_with_realtime_output(cmd_s6000, "S6000")

        # --- 数据处理流程 ---
        if not os.path.exists(s6000_result_file):
            raise FileNotFoundError(f"S6000 任务执行完毕，但未在预期路径找到结果文件: {s6000_result_file}")

        with open(s6000_result_file, 'r', encoding='utf-8') as f:
            s6000_data = json.load(f)

        if not s6000_data.get('success'):
            raise ValueError(f"S6000 任务报告失败: {s6000_data.get('error', '未知错误')}")
        
        titles = s6000_data.get('processed_titles', [])
        if not titles:
            success_message = "S6000 任务成功完成，但没有需要发送的新项目。"
            print(f"[信息] {success_message}")
            last_task_status.update({"status": "success", "message": success_message, "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")})
            return

        print(f"[*] S6000处理完成，获取到 {len(titles)} 个项目。")

        # --- 阶段 2: 准备并执行 RPA 任务 (实时显示输出) ---
        rpa_task_data = {
            'task_type': task_type,
            'recipient': recipient_list,
            'titles': titles,
            'download_dir': s6000_data.get('download_directory', '')
        }
        
        rpa_data_file = 'rpa_task_data.json'
        with open(rpa_data_file, 'w', encoding='utf-8') as f:
            json.dump(rpa_task_data, f, ensure_ascii=False, indent=2)
        
        cmd_rpa = ['python', '-u', RPA_SCRIPT_PATH, '--data-file', rpa_data_file]
        run_subprocess_with_realtime_output(cmd_rpa, "RPA")

        success_message = f"RPA 任务成功完成，已向 '{recipient_list}' 发送 {len(titles)} 个项目。"
        print(f"[成功] {success_message}")
        last_task_status.update({"status": "success", "message": success_message, "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")})

    except Exception as e:
        error_message = f"自动化流程发生错误: {e}"
        print(f"[严重错误] {error_message}")
        last_task_status.update({"status": "error", "message": str(e), "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")})
    finally:
        task_lock.release()
        print(f"任务 {task_type} 流程结束，已释放任务锁。")


@app.route('/start-task', methods=['POST'])
def start_task_endpoint():
    global last_task_status
    if not task_lock.acquire(blocking=False):
        return jsonify({"status": "error", "message": "系统正忙，已有任务在运行中。"}), 409
    try:
        data = request.get_json()
        if not data or 'task_type' not in data:
            return jsonify({"status": "error", "message": "请求体错误, 缺少 'task_type' 参数。"}), 400
        task_type = data.get('task_type')
        recipient = data.get('recipient', ["wlaqjkfz"])
        if not isinstance(recipient, list):
            return jsonify({"status": "error", "message": "'recipient' 参数必须是一个列表。"}), 400
        last_task_status = {
            "status": "running", "task_type": task_type,
            "message": f"任务 '{task_type}' 已启动，正在执行中...",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        print(f"[*] 接收到任务请求: {task_type} -> {recipient}。任务锁已激活。")
        task_thread = threading.Thread(target=run_full_automation_flow, args=(task_type, recipient))
        task_thread.start()
        return jsonify({
            "status": "accepted",
            "message": f"任务 '{task_type}' 已接受。请通过 /task-status 查询最终结果。"
        }), 202
    except Exception as e:
        if task_lock.locked():
            task_lock.release()
        return jsonify({"status": "error", "message": f"处理请求时发生错误: {e}"}), 500


@app.route('/task-status', methods=['GET'])
def task_status_endpoint():
    return jsonify(last_task_status), 200


if __name__ == '__main__':
    print("="*40)
    print("RPA Agent 正在启动...")
    print(f"监听地址: http://0.0.0.0:6000")
    print("  - POST /start-task  (启动新任务)")
    print("  - GET  /task-status (查询任务状态)")
    print("="*40)
    app.run(host='0.0.0.0', port=6000, debug=False)