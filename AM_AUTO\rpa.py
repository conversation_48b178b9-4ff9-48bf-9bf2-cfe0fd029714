# rpa_task.py

import pyautogui
import time
import os
import argparse
import json
import glob
import sys
import re
from datetime import datetime

# --- 图像路径 ---
IMAGE_FOLDER_PATH = "C:\\Users\\<USER>\\Desktop\\AM_AUTO\\image"

# 添加命令行参数支持
def parse_arguments():
    parser = argparse.ArgumentParser(description="RPA自动化发送")
    parser.add_argument('--data-file', help='任务数据文件路径')
    return parser.parse_args()

def load_task_data(data_file):
    """从文件加载任务数据"""
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"[错误] 加载任务数据失败: {e}")
        return None

def get_latest_files_by_titles(download_dir, titles, task_type):
    """根据标题获取对应的最新下载文件"""
    files_to_send = []
    
    if task_type in ['notice', 'task', 'contact_sheet']:
        # 前三个任务：文件名与标题一致
        for title in titles:
            pattern = os.path.join(download_dir, f"*{title}*")
            matching_files = glob.glob(pattern)
            if matching_files:
                # 选择最新的文件
                latest_file = max(matching_files, key=os.path.getctime)
                files_to_send.append(latest_file)
            else:
                print(f"[警告] 未找到标题 '{title}' 对应的文件")
                files_to_send.append(None)
    else:
        # 后两个任务：按下载时间排序对应
        all_files = glob.glob(os.path.join(download_dir, "*"))
        all_files.sort(key=os.path.getctime, reverse=True)  # 最新的在前
        
        # 取前N个文件对应N个标题
        files_to_send = all_files[:len(titles)]
    
    return files_to_send

def open_yanzi_and_select_contact(recipient):
    """打开小燕子并选择联系人"""
    """ 可选联系人： fuzhi """
    # 步骤 1: 点击 "小燕子" 桌面右下角图标
    if not click_image_template(image_name="am_icon.png", description="小燕子图标"):
        print("[任务中止] 关键步骤 '打开小燕子' 失败，任务无法继续。")
        return False # 中止任务

    # 步骤 2: 点击 "信息通信分公司"分组 
    time.sleep(1) # 等待菜单弹出
    xintong_list = ["xintong_0.png", "xintong_1.png"]
    if not click_any_of_image(xintong_list, description="信息通信分公司分组"):
        print("[任务中止] 关键步骤 '点击信息通信分公司分组' 失败，任务无法继续。")
        return False
    
    # 步骤 3: 选择联系人打开对话框
    time.sleep(1)
    contact_image = f"contact_{recipient}.png"
    if not click_image_template(image_name=contact_image, description=f"联系人{recipient}", clicks=2):
        # 如果第一次未找到 "联系人"，点击 "网络安全监控班"展开列表
        wajiankong_list = ["wajiankong.png", "wajiankong_1.png", "wajiankong_2.png"]
        if not click_any_of_image(wajiankong_list, description="网络安全监控班", clicks=2):
            print("[任务中止] 关键步骤 '选择网络安全监控班' 失败，任务无法继续。")
            return False

        time.sleep(1)
        if not click_image_template(image_name=contact_image, description=f"联系人{recipient}", clicks=2):
            print("[任务中止] 关键步骤 '打开副值对话框' 失败，任务无法继续。")
            return False
    
    time.sleep(1)
    return True

def select_multiple_contacts(recipient_list):
    """
    在群发模式下，展开分组并选择多个联系人。

    参数:
    recipient_list (list): 包含所有待选择联系人姓名的列表。

    返回:
    bool: 如果成功完成选择并点击确定，则返回 True，否则返回 False。
    """
    print("[*] 开始选择群发联系人...")

    # 步骤 2.1: 点击“添加联系人”或类似按钮，打开联系人选择界面
    if not click_image_template(image_name="add_recipients.png", description="打开联系人选择界面"):
        return False
    time.sleep(1)

    # 步骤 2.2: 展开组织机构和分组
    # - "expand_contact_list.png": 用于展开整个联系人列表的箭头
    # - "zuzhijigou_1.png","zuzhijigou_2.png": 用于选中“组织机构”这个特定分组的按钮
    # - "group_xintong.png": 分组信息通信分公司的文字
    # - "group_jsabs.png": 分组技术安保室的文字
    print("  - 展开分组...")
    if not click_image_template(image_name="expand_contact_list.png", description="展开联系人列表"): return False
    time.sleep(0.5)
    zuzhijigou_list = ["zuzhijigou_1.png", "zuzhijigou_2.png"]
    if not click_any_of_image(zuzhijigou_list, description="展开组织机构列表"): return False
    time.sleep(0.5)
    if not click_image_template(image_name="group_xintong.png", description="信息通信分公司分组", clicks=2): return False
    time.sleep(0.5)
    if not click_image_template(image_name="group_jsabs.png", description="技术安保室分组", clicks=2): return False
    time.sleep(0.5)

    # 步骤 2.3: 第一次尝试选择联系人
    print("  - 第一轮联系人选择，从技术安保室分组寻找...")
    wanganjiankong_contacts = []

    for name in recipient_list:
        # 联系人姓名前面的复选框位于其左侧30像素处
        CLICK_OFFSET_X = -30 
        try:
            contact_image = f"mass_contact_{name}.png"
            # 查找联系人图片，并点击其左侧的复选框
            location_box = pyautogui.locateOnScreen(os.path.join(IMAGE_FOLDER_PATH, contact_image), confidence=0.9)
            
            click_x = location_box.left + CLICK_OFFSET_X
            click_y = location_box.top + location_box.height / 2
            pyautogui.click(click_x, click_y)
            print(f"    [成功] 已选择联系人: {name} （点击坐标： x={int(click_x)}，y={int(click_y)}）")
            time.sleep(0.3)
        except pyautogui.ImageNotFoundException:
            print(f"    [提示] 在当前视图未找到联系人: {name}")
            wanganjiankong_contacts.append(name)
    
    # 关闭技术安保室分组，并展开网络安全监控班分组
    if not click_image_template(image_name="group_jsabs.png", description="技术安保室分组", clicks=2): return False
    time.sleep(0.5)
    if not click_image_template(image_name="group_wanganjiankong.png", description="网络安全监控班分组", clicks=2): return False
    time.sleep(0.5)
    
    print("  - 第二轮联系人选择，从网络安全监控班分组寻找...")
    not_found_contacts = []        
    for name in wanganjiankong_contacts:
        # 联系人姓名前面的复选框位于其左侧30像素处
        CLICK_OFFSET_X = -30 
        try:
            contact_image = f"mass_contact_{name}.png"
            if name == "wlaqjkfz":
                    CLICK_OFFSET_X = -100
            # 查找联系人图片，并点击其左侧的复选框
            location_box = pyautogui.locateOnScreen(os.path.join(IMAGE_FOLDER_PATH, contact_image), confidence=0.9)
            
            click_x = location_box.left + CLICK_OFFSET_X
            click_y = location_box.top + location_box.height / 2
            pyautogui.click(click_x, click_y)
            print(f"    [成功] 已选择联系人: {name} （点击坐标： x={int(click_x)}，y={int(click_y)}）")
            time.sleep(0.3)
        except pyautogui.ImageNotFoundException:
            print(f"    [提示] 在当前视图未找到联系人: {name}")
            not_found_contacts.append(name)
    
    # 步骤 2.4: 如果有未找到的联系人，则向下滑动并再次尝试
    if not_found_contacts:
        print("  - 有未找到的联系人，开始向下滚动...")
        if not click_image_template(image_name="scroll_down.png", description=f"向下滚动十次", clicks=10):
            print("    [警告] 未找到向下滚动箭头。")
            return False
        time.sleep(0.2)
        
        print("  - 第二轮联系人选择...")
        remaining_not_found = []
        for name in not_found_contacts:
            # 联系人姓名前面的复选框位于其左侧30像素处
            CLICK_OFFSET_X = -30 
            try:
                contact_image = f"mass_contact_{name}.png"
                if name == "wlaqjkfz":
                    CLICK_OFFSET_X = -100
                
                location_box = pyautogui.locateOnScreen(os.path.join(IMAGE_FOLDER_PATH, contact_image), confidence=0.9)

                click_x = location_box.left + CLICK_OFFSET_X
                click_y = location_box.top + location_box.height / 2
            
                pyautogui.click(click_x, click_y)
                print(f"    [成功] 已选择联系人: {name} （点击坐标： x={int(click_x)}，y={int(click_y)}）")
                time.sleep(0.3)
            except pyautogui.ImageNotFoundException:
                print(f"    [失败] 滚动后仍未找到联系人: {name}")
                remaining_not_found.append(name)
        
        if remaining_not_found:
            print(f"[警告] 最终未找到的联系人: {', '.join(remaining_not_found)}")

    # 步骤 2.5: 点击“确定”按钮，完成选择
    print("  - 点击确定，完成选择...")
    if not click_image_template(image_name="mass_confirm.png", description="确定选择联系人"):
        return False
    time.sleep(0.3)
    print("[*] 联系人选择完成。")
    return True

def attach_single_files(file_path):
    """
    群发模式下的辅助函数：附加单个文件
    """
    print(f"    ->正在附加文件： {os.path.basename(file_path)}")
    
    if not click_image_template(image_name="mass_file.png", description="打开文件对话框的按钮"):
        return False
    time.sleep(1)
    
    if not input_text(image_name="file_path_input.png", description="文件路径输入框", text_to_input=file_path):
        return False
    time.sleep(1)
    
    if not click_image_template(image_name="file_confirm.png", description="确认选择文件"):
        return False
    
    print(f"[成功] 当前文件已附加。")
    time.sleep(1) # 等待程序处理文件列表
    return True
    
def attach_multiple_files(file_paths):
    """
    一次性附加多个文件。

    通过将所有文件路径格式化成一个带引号的、空格分隔的字符串，
    可以一次性将它们全部输入到文件选择框中，实现多选效果。

    参数:
    file_paths (list): 包含所有待附加文件路径的列表。

    返回:
    bool: 如果成功附加则返回 True，否则返回 False。
    """
    # 步骤1: 筛选出所有有效、存在的文件路径
    valid_files = [f for f in file_paths if f and os.path.exists(f)]
    
    if not valid_files:
        print("[信息] 列表中没有有效的附件，跳过文件附加步骤。")
        return True # 没有文件需要附加，视为成功

    print(f"[*] 准备循环附加 {len(valid_files)} 个文件...")

    for i, file in enumerate(valid_files):
        print(f"    - 附加第 {i+1}/{len(valid_files)} 个文件...")
        if not attach_single_files(file):
            print(f"[警告] 附加文件 {os.path.basename(file)}时失败，跳过此文件。")
            continue
    
    print(f"[成功] {len(valid_files)} 个文件已附加。")
    time.sleep(1) # 等待程序处理文件列表
    return True

def send_text_message(message):
    """发送文本消息"""
    print(f"[*] 发送文本消息: {message}")

    if not input_text(image_name="send.png", description="对话输入框",text_to_input=message,click_offset=(0,-60)):
        print("[任务中止] 关键步骤 '输入消息' 失败，任务无法继续。")
        return False
    time.sleep(0.5)
    
    # 发送消息 - 需要截图 "send.png"
    if not click_image_template(image_name="send.png", description="发送消息按钮"):
        return False
    
    time.sleep(1)
    return True

def send_files_via_yanzi(recipient, titles, files):
    """通过小燕子发送文件"""
    print(f"\n准备向 {recipient} 发送 {len(files)} 个文件...")
    
    # 步骤1-3: 打开小燕子和选择联系人
    if not open_yanzi_and_select_contact(recipient=recipient):
        return False
        
    text1 = "测试消息请忽略（智能网安运维项目开发测试）"
    if not input_text(image_name="send.png", description="对话输入框",text_to_input=text1,click_offset=(0,-60)):
        print("[任务中止] 关键步骤 '输入消息' 失败，任务无法继续。")
        return
        
    # 发送测试消息说明 - 需要截图 "send.png"
    if not click_image_template(image_name="send.png", description="发送消息按钮"):
        return False
    
    # 步骤4: 发送每个文件和对应标题
    for i, (title, file_path) in enumerate(zip(titles, files)):
        # 一定会发送标题消息
        if not send_text_message(f"【{title}】"):
            print("[警告] '{title}' 发送失败，跳过此项。")
            continue
            
        if file_path is None:
            print(f"跳过第 {i+1}/{len(files)} 个文件: {title} (无附件)")
            continue
            
        print(f"发送第 {i+1}/{len(files)} 个文件: {title}")        
        # 发送文件
        if not send_file(file_path):
            print(f"[警告] 第 {i+1}/{len(files)} 个文件发送失败，跳过此项。")
            continue
            
        time.sleep(1)  # 间隔发送
    
    # 关闭对话框
    close_dialog()
    return True

def mass_send_via_yanzi(recipient, titles, files):
    """通过小燕子消息模式群发文件"""
    print(f"\n准备向网络安全监控班群发 {len(files)} 个文件...")
    
    # 步骤1: 打开小燕子和选择联系人，默认通过副值窗口进入对话
    print("[*] 步骤1: 打开小燕子，默认通过副值窗口进入对话...")
    if not open_yanzi_and_select_contact(recipient="fuzhi"):
        return False
        
    # 关闭小燕子，防止小燕子联系人与消息模式下选择联系人冲突
    if not click_image_template(image_name="close_am.png", description="关闭小燕子按钮"):
        print("[任务中止] 未能关闭小燕子，任务无法继续。")
        return False
        
    # 步骤2: 点击进入消息模式
    if not click_image_template(image_name="mass_mode.png", description="消息模式按钮"):
        print("[任务中止] 未能进入消息模式，任务无法继续。")
        return False
    time.sleep(1) # 等待界面切换
    
    # 检查'recipient' 参数，现在应该是一个列表，例如 ['zhuzhi', 'fuzhi']
    if not isinstance(recipient, list):
        print(f"[错误] 群发模式需要一个联系人列表作为 'recipient' 参数，但收到了 {type(recipient)}。")
        return False
    if not select_multiple_contacts(recipient):
        print("[任务中止] 选择联系人失败，任务无法继续。")
        return False
        
    # 步骤 3: 准备并输入所有文本内容
    print("[*] 步骤3: 输入所有标题文本...")
    text1 = "测试消息请忽略（智能网安运维项目开发测试）"
    # 将所有标题用换行符合并，并在开头加上测试文本
    all_titles_text = "\n\n".join(titles)
    full_message = f"{text1}\n\n{all_titles_text}"

    if not input_text(image_name="mass_file.png", description="消息模式对话输入框",text_to_input=full_message,click_offset=(0,-60)):
        print("[任务中止] 关键步骤 '输入消息' 失败，任务无法继续。")
        return False
    
    # 步骤 4: 附加所有文件
    print("[*] 步骤4: 附加所有文件...")
    if not attach_multiple_files(files):
        print("[任务中止] 附加文件失败，任务无法继续。")
        return False

    time.sleep(1)
    # 步骤 5: 点击最终的发送按钮
    print("[*] 步骤5: 发送最终消息...")
    send_list = ["send.png", "mass_send.png"]
    if not click_any_of_image(send_list, description="最终发送按钮"):
        print("[任务中止] 点击最终发送按钮失败。")
        return False
    
    print("[成功] 群发任务已成功发送！")
    # 消息模式发送后自动关闭对话框
    return True

def send_file(file_path):
    """发送单个文件"""
    print(f"[*] 准备发送文件: {file_path}")
    
    # 1. 点击文件发送按钮 - 需要截图 "file_send.png"
    if not click_image_template(image_name="file_send.png", description="文件发送按钮"):
        return False
    
    time.sleep(1)
    
    # 2. 在文件选择对话框中输入文件路径 - 需要截图 "file_path_input.png"
    if not input_text(image_name="file_path_input.png", description="文件路径输入框", text_to_input=file_path):
        return False
    
    time.sleep(0.5)
    
    # 3. 点击打开，选中文件 - 需要截图 "file_confirm.png"
    if not click_image_template(image_name="file_confirm.png", description="确认发送"):
        return False
        
    # 4. 点击发送 - 需要截图 "send.png"
    if not click_image_template(image_name="send.png", description="发送消息按钮"):
        return False
        
    time.sleep(2)
    return True

def close_dialog():
    """关闭对话框"""
    print("[*] 关闭对话框")
    if not click_image_template(image_name="close_input.png", description="关闭按钮"):
        print("[任务中止] 关键步骤 '关闭对话框' 失败，任务无法继续。")
        return False
        
    time.sleep(2) # 等待加载
    return True
    
# --- 核心功能模板：图像点击函数 ---
def click_image_template(
    image_name,
    description,
    retries=5,
    retry_interval=1,
    confidence=0.9,
    clicks=1,
    interval=0.0
):
    """
    一个模板化的函数，用于查找并点击屏幕上的图像。

    参数:
    image_name (str): 要查找的模板图片的文件名 (例如 "login_button.png")。
    description (str): 对这个操作的人类可读描述，用于打印日志。
    retries (int): 如果找不到图像，要重试的次数。
    retry_interval (int): 每次重试之间等待的秒数。
    confidence (float): 图像匹配的置信度，范围0.0到1.0。数值越高越精确。
                       对于不同分辨率或抗锯齿效果，可能需要适当调低此值。
    clicks (int): 点击次数
    interval (float): 点击间隔

    返回:
    bool: 如果成功点击则返回 True，否则返回 False。
    """
    # 拼接图片的完整路径
    image_path = os.path.join(IMAGE_FOLDER_PATH, image_name)

    if not os.path.exists(image_path):
        print(f"[错误] 模板图片不存在: {image_path}")
        return False

    print(f"[*] 准备点击: '{description}' (使用图片: {image_name})")

    for i in range(retries):
        try:
            # 在屏幕上查找图像的中心点坐标
            location = pyautogui.locateCenterOnScreen(image_path, confidence=confidence)

            if location:
                print(f"[成功] 在坐标 {location} 找到了 '{description}'。")
                pyautogui.click(location, clicks=clicks, interval=interval)
                print(f"[成功] 已点击 '{description}'。")
                return True
            else:
                # 如果 location is None, 说明没找到
                if i < retries - 1:
                    print(f"[提示] 未找到 '{description}'。将在 {retry_interval} 秒后重试... (第 {i+1}/{retries} 次)")
                    time.sleep(retry_interval)
                else:
                    # 最后一次重试依然失败
                    print(f"[失败] 经过 {retries} 次尝试后，仍未找到 '{description}'。")
                    return False
        except Exception as e:
            print(f"[严重错误] 在查找或点击 '{description}' 时发生异常: {e}")
            return False
            
    return False # 循环结束仍未成功

# --- 核心功能扩展：多状态图像点击函数 ---
def click_any_of_image(image_list, description, **kwargs):
    """
    一个扩展的函数，当一处图像会变化时，从所有可能的图像中查找匹配图像，并点击屏幕上的图像。

    参数:
    image_list (str list): 要查找的模板图片的文件名列表 (例如 ["1.png", "2.png"])。
    description (str): 对这个操作的人类可读描述，用于打印日志。
    **kwargs: 其他可传递给click_image_template的参数（如retries=, retry_interval=, confidence）

    返回:
    bool: 如果成功点击则返回 True，否则返回 False。
    """

    print(f"[*] 准备点击: '{description}' (尝试图片: {image_list})")

    for image_name in image_list:
        # 列表循环重试减少到3次
        if 'retries' not in kwargs:
            kwargs['retries'] = 3
        if click_image_template(image_name, description=f"{description} ({image_name})", **kwargs):
            return True

    print(f"[失败] 未能在列表中找到任何一个 '{description}' 的有效状态。")        
    return False # 循环结束仍未成功

# --- 核心功能扩展：文本输入函数 ---
def input_text(
    image_name,
    description,
    text_to_input,
    click_offset=(0, 50),
    method='paste',
    retries=5,
    retry_interval=1,
    confidence=0.9,
):
    try:
        import pyperclip
    except ImportError:
        print("未找到pyperclip库")
        return False

    print(f"[*] 准备在 '{description}' 中输入文本，使用'{method}'方法...")

    image_path = os.path.join(IMAGE_FOLDER_PATH, image_name)
    if not os.path.exists(image_path):
        print(f"[错误] 模板图片不存在: {image_path}")
        return False

    for i in range(retries):
        try:
            anchor_location = pyautogui.locateCenterOnScreen(image_path, confidence=confidence)
            if anchor_location:
                print(f"[成功] 在坐标 {anchor_location} 找到了 '{description}' 的锚点。")
                target_x = anchor_location.x + click_offset[0]
                target_y = anchor_location.y + click_offset[1]
                pyautogui.click(target_x, target_y)
                time.sleep(1)
                print("[成功] 已点击到输入框。")
                print("[*] 正在输入... ")

                if method == 'paste':
                    pyperclip.copy(text_to_input)
                    pyautogui.hotkey('ctrl', 'v')
                elif method == 'typewrite':
                    pyautogui.typewrite(text_to_input, interval=0.05)
                else:
                    print(f"[错误] 无效的输入方式：'{method}'。")
                    return False

                print(f"[成功] 已在 '{description}'中完成输入。")
                return True

        except Exception as e:
            print(f"[严重错误] 操作时发生异常: {repr(e)}")
            return False

        if i < retries -1 :
            print(f"[提示] 未找到 '{description}' 的锚点。将在 {retry_interval}秒后重试... (第 {i+1}/{retries}次)")
            time.sleep(retry_interval)
        else:
            print(f"[失败] 经过 {retries} 次尝试后，仍未找到 '{description}' 的锚点。")
            return False
    return False

# --- 主要业务流程 ---
def perform_main_task():
    """主任务流程 - 从文件读取数据"""
    args = parse_arguments()
    
    if args.data_file:
        # 从文件读取任务数据
        task_data = load_task_data(args.data_file)
        if task_data:
            task_type = task_data['task_type']
            recipient = task_data['recipient']
            titles = task_data['titles']
            download_dir = task_data['download_dir']
            
            if titles:
                files = get_latest_files_by_titles(download_dir, titles, task_type)
                mass_send_via_yanzi(recipient, titles, files)
            else:
                print("[信息] 没有需要发送的文件")
        else:
            print("[错误] 无法加载任务数据")
    else:
        print("请使用 --data-file 参数指定数据文件")

if __name__ == '__main__':

    print("RPA 任务将在3秒后开始...请准备好目标程序窗口。")
    time.sleep(3)
    
    titles =[
    "预警事项：北京天融信科技有限公司天融信上网行为管理系统存在命令执行漏洞(CNVD-2025-14769)风险预警\n监测发现，北京天融信科技有限公司天融信上网行为管理系统存在命令执行漏洞，攻击者可利用漏洞执行命令。\n天融信上网行为管理系统是一款网络行为管理产品，旨在满足各行各业进行网络行为管理和内容审计的需求。",
    "深信服终端监测响应平台（EDR）管理平台存在远程命令执行漏洞（CVE-2025-34041）。该漏洞允许未经身份验证的攻击者构建恶意 HTTP 请求并将其发送到 EDR Manager 界面，从而导致远程执行任意命令。",
    "监测发现，sudo发布安全公告，修复了存在于sudo中2个安全漏洞。具体漏洞信息如下：\nsudo chroot权限提升漏洞（CVE-2025-32463）\nchroot，即change root directory (更改root目录)是一个sudo指令的命令，它可以改变当前进程及其子进程的根目录。攻击者无需系统预先为其设定任何 sudo规则，仅需控制可写的不受信任路径，通过chroot () 操作，就能让Sudo以root权限执行相关命令。\nsudo host权限提升漏洞（CVE-2025-32462）\nhost是Linux中sudo命令程序得一个选项，可让用户查看其他主机权限， sudo的 --host选项错误应用远程主机规则到本地，攻击者可绕过权限提升至root并执行任意代码。\nsudo是linux系统管理指令，是允许系统管理员让普通用户执行一些或者全部的root命令的一个工具。"
    ]
    download_dir = "C:\\Users\\<USER>\\Desktop\\AM_AUTO\\download"
    task_type = "warning_notice"
    recipient = "副值"
    files = get_latest_files_by_titles(download_dir, titles, task_type)
    print(files)
    #send_files_via_yanzi(recipient, titles, files)
    #click_image_template(image_name="group_wanganjiankong.png", description="网络安全监控班分组", clicks=2)
    recipients_to_send = ["lc", "wlaqjkzz", "tc", "wlaqjkfz"] 
    mass_send_via_yanzi(recipients_to_send, titles, files)
    """
    perform_main_task()
    """
