#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试类型转换修复是否有效
"""

import sys
import os
import asyncio

# 添加main目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'main'))

from main.run import query_alerts


async def test_type_conversion():
    """
    测试不同类型的参数是否能正确处理
    """
    print("=" * 60)
    print("类型转换测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "name": "字符串类型参数",
            "hours_ago": "1",
            "limit": "10"
        },
        {
            "name": "整数类型参数", 
            "hours_ago": 1,
            "limit": 10
        },
        {
            "name": "浮点数类型参数",
            "hours_ago": 1.0,
            "limit": 10.0
        },
        {
            "name": "None值参数",
            "hours_ago": None,
            "limit": None
        }
    ]
    
    base_url = "https://10.228.16.244"
    username = "admin"
    password = "test_password"  # 这里用假密码测试类型转换，不会真正登录
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"  hours_ago: {test_case['hours_ago']} (类型: {type(test_case['hours_ago']).__name__})")
        print(f"  limit: {test_case['limit']} (类型: {type(test_case['limit']).__name__})")
        
        try:
            result = await query_alerts(
                base_url=base_url,
                username=username,
                password=password,
                hours_ago=test_case['hours_ago'],
                limit=test_case['limit']
            )
            
            if result["status"] == 2 and "参数类型错误" in result["result"]:
                print("  ❌ 类型转换失败")
            elif result["status"] == 2 and ("登录" in result["result"] or "网络" in result["result"]):
                print("  ✅ 类型转换成功（登录失败是预期的）")
            else:
                print(f"  ✅ 类型转换成功，结果: {result['status']}")
                
        except Exception as e:
            if "unsupported operand type" in str(e):
                print(f"  ❌ 类型错误未修复: {e}")
            else:
                print(f"  ✅ 类型转换成功（其他错误: {e}）")


def test_time_calculation():
    """
    测试时间计算函数
    """
    print("\n" + "=" * 60)
    print("时间计算测试")
    print("=" * 60)
    
    import time
    
    def safe_time_calculation(hours_ago):
        """安全的时间计算"""
        try:
            hours_ago = int(hours_ago) if hours_ago is not None else 1
            current_time = int(time.time() * 1000)
            start_time = current_time - (hours_ago * 60 * 60 * 1000)
            return start_time, current_time
        except (ValueError, TypeError) as e:
            return None, f"错误: {e}"
    
    test_values = ["1", 1, 1.0, None, "abc", ""]
    
    for value in test_values:
        print(f"\n测试值: {value} (类型: {type(value).__name__})")
        start_time, current_time = safe_time_calculation(value)
        
        if isinstance(current_time, str):  # 错误情况
            print(f"  ❌ {current_time}")
        else:
            print(f"  ✅ 计算成功")
            print(f"    开始时间: {start_time}")
            print(f"    结束时间: {current_time}")


def main():
    """主测试函数"""
    print("天眼告警查询APP - 类型转换修复测试")
    
    # 测试时间计算
    test_time_calculation()
    
    # 测试异步函数（需要密码才能真正测试）
    print("\n" + "=" * 60)
    print("如需测试完整流程，请在test_app.py中填入正确的密码")
    print("=" * 60)


if __name__ == "__main__":
    main()
