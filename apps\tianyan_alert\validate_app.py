#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天眼告警查询APP验证脚本
检查app结构和依赖是否正确
"""

import os
import sys
import json

def check_app_structure():
    """检查app目录结构"""
    print("🔍 检查APP目录结构...")
    
    required_files = [
        'app.json',
        'readme.md', 
        'icon.png',
        'main/__init__.py',
        'main/run.py',
        'main/login.py',
        'main/query_list.py',
        'main/alert_detail.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 目录结构完整")
        return True


def check_app_config():
    """检查app.json配置"""
    print("🔍 检查APP配置文件...")
    
    try:
        with open('app.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['identification', 'name', 'version', 'description', 'type', 'action', 'args']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            print(f"❌ app.json缺少字段: {missing_keys}")
            return False
        
        # 检查action配置
        if not config['action'] or not config['action'][0].get('func') == 'query_alerts':
            print("❌ action配置不正确")
            return False
        
        print("✅ 配置文件正确")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def check_dependencies():
    """检查依赖模块"""
    print("🔍 检查依赖模块...")
    
    required_modules = [
        'requests',
        'ddddocr', 
        'loguru',
        'urllib3'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {missing_modules}")
        print(f"请运行: pip install {' '.join(missing_modules)}")
        return False
    else:
        print("✅ 依赖模块完整")
        return True


def check_imports():
    """检查模块导入"""
    print("🔍 检查模块导入...")
    
    try:
        # 添加main目录到路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'main'))
        
        # 测试导入主要模块
        from main.run import query_alerts, unix_timestamp_to_beijing_time, map_table_name_to_chinese
        from main.login import QianXinLoginSimulator
        from main.query_list import AlertQueryManager, create_query_manager
        from main.alert_detail import AlertDetailManager
        
        print("✅ 模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_utility_functions():
    """测试工具函数"""
    print("🔍 测试工具函数...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'main'))
        from main.run import unix_timestamp_to_beijing_time, map_table_name_to_chinese
        
        # 测试时间转换
        test_timestamp = 1721534400  # 2024-07-21 11:00:00
        formatted_time = unix_timestamp_to_beijing_time(test_timestamp)
        print(f"  时间转换测试: {test_timestamp} -> {formatted_time}")
        
        # 测试告警类型映射
        test_mappings = [
            ('webids_alert', '网页漏洞利用'),
            ('ips_alert', '网络攻击'),
            ('unknown_alert', '其他攻击')
        ]
        
        for original, expected in test_mappings:
            result = map_table_name_to_chinese(original)
            print(f"  类型映射测试: {original} -> {result}")
            if result != expected:
                print(f"❌ 映射错误，期望: {expected}")
                return False
        
        print("✅ 工具函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False


def main():
    """主验证函数"""
    print("=" * 60)
    print("天眼告警查询APP验证")
    print("=" * 60)
    
    # 切换到app目录
    app_dir = os.path.dirname(__file__)
    if app_dir:
        os.chdir(app_dir)
    
    checks = [
        check_app_structure,
        check_app_config,
        check_dependencies,
        check_imports,
        test_utility_functions
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 检查异常: {e}")
            print()
    
    print("=" * 60)
    print(f"验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 APP验证通过，可以正常使用！")
    else:
        print("⚠️  APP存在问题，请根据上述提示进行修复")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
