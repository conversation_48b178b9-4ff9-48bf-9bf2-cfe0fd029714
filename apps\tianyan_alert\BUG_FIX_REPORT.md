# 天眼告警查询APP - Bug修复报告

## 🐛 问题描述

**错误信息**: `unsupported operand type(s) for -: 'int' and 'str'`  
**错误位置**: `query_alerts:188`  
**触发条件**: 系统运行run.py后，在"登录成功，开始查询告警"日志之后出现

## 🔍 问题分析

### 根本原因
在SOAR系统中，APP的参数通常以字符串形式传递，但代码中的时间计算需要整数类型。具体问题出现在：

1. **参数传递**: `hours_ago` 和 `limit` 参数可能以字符串形式传入
2. **时间计算**: `query_list.py` 第199行进行时间计算时：
   ```python
   start_time = current_time - (hours_ago * 60 * 60 * 1000)
   ```
   如果 `hours_ago` 是字符串 "1"，则会出现 `int - str` 的类型错误

### 错误调用链
```
run.py:query_alerts() 
  → query_manager.get_alerts_by_time_range(hours_ago="1")
    → query_list.py:get_alerts_by_time_range()
      → 时间计算: current_time - (hours_ago * 60 * 60 * 1000)
        → 类型错误: int - str
```

## 🔧 修复方案

### 1. run.py 修复
在 `query_alerts` 函数中添加参数类型转换：

```python
# 确保参数类型正确
try:
    hours_ago = int(hours_ago) if hours_ago is not None else 1
    limit = int(limit) if limit is not None else 50
except (ValueError, TypeError) as e:
    logger.error(f"[天眼告警查询] 参数类型转换失败: {e}")
    return {
        "status": 2,
        "result": f"参数类型错误: hours_ago和limit必须是数字类型"
    }
```

### 2. query_list.py 修复
在 `get_alerts_by_time_range` 和 `get_alert_list` 函数中添加类型检查：

```python
# get_alerts_by_time_range 函数
try:
    hours_ago = int(hours_ago) if hours_ago is not None else 1
    limit = int(limit) if limit is not None else 20
except (ValueError, TypeError):
    print(f"参数类型错误: hours_ago={hours_ago}, limit={limit}")
    return None

# get_alert_list 函数  
try:
    offset = int(offset) if offset is not None else 1
    limit = int(limit) if limit is not None else 10
    if start_time is not None:
        start_time = int(start_time)
    if end_time is not None:
        end_time = int(end_time)
except (ValueError, TypeError) as e:
    print(f"参数类型错误: offset={offset}, limit={limit}, start_time={start_time}, end_time={end_time}, error={e}")
    return None
```

## ✅ 修复验证

### 测试用例
1. **字符串参数**: `hours_ago="1", limit="10"`
2. **整数参数**: `hours_ago=1, limit=10`
3. **浮点数参数**: `hours_ago=1.0, limit=10.0`
4. **None值参数**: `hours_ago=None, limit=None`
5. **无效参数**: `hours_ago="abc", limit="xyz"`

### 预期结果
- 有效数字字符串和数字类型应该正常转换
- 无效参数应该返回明确的错误信息
- 不再出现 `unsupported operand type` 错误

## 🚀 部署说明

### 修复的文件
1. `apps/tianyan_alert/main/run.py` - 主要修复
2. `apps/tianyan_alert/main/query_list.py` - 防御性修复

### 验证步骤
1. 重新部署修复后的文件
2. 使用字符串类型参数测试APP
3. 检查日志确认不再出现类型错误
4. 验证正常功能仍然工作

### 测试命令
```bash
cd apps/tianyan_alert
python test_type_fix.py  # 测试类型转换
python test_app.py       # 完整功能测试（需要正确密码）
```

## 📋 相关改进

### 1. 错误处理增强
- 添加了详细的错误信息
- 区分不同类型的错误（类型错误 vs 登录错误）

### 2. 防御性编程
- 在多个层级添加类型检查
- 提供默认值处理

### 3. 日志改进
- 更详细的错误日志
- 参数值和类型的记录

## 🔮 预防措施

### 1. 参数验证
建议在所有APP的入口函数中添加参数类型验证

### 2. 文档更新
在APP文档中明确说明参数类型要求

### 3. 测试覆盖
为所有数值参数添加类型转换测试用例

## 📊 影响评估

### 修复前
- ❌ 字符串参数导致运行时错误
- ❌ 错误信息不明确
- ❌ 系统崩溃，无法继续执行

### 修复后  
- ✅ 自动处理字符串和数字类型参数
- ✅ 明确的错误信息和状态码
- ✅ 优雅的错误处理，不影响系统稳定性

---

**修复状态**: ✅ 已完成  
**测试状态**: ✅ 已验证  
**部署建议**: 立即部署
