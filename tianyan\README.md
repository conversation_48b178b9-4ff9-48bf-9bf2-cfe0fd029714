# 奇安信流量传感器 API 工具

一个用于自动化操作奇安信流量传感器系统的Python工具包，支持自动登录、告警查询、详情获取等功能。

## 🚀 功能特性

- **自动登录**: 支持验证码自动识别和登录重试机制
- **告警查询**: 多种条件查询告警列表，支持时间范围、危险级别、IP地址等过滤
- **详情获取**: 根据告警ID获取详细的数据包信息、网络请求响应等
- **数据导出**: 支持将查询结果导出为JSON格式
- **统计分析**: 提供告警统计和分析功能

## 📋 系统要求

- Python 3.6+
- 依赖库：
  - `requests` - HTTP请求库
  - `ddddocr` - 验证码识别库

## 🛠️ 安装

1. 克隆或下载项目文件
2. 安装依赖：

```bash
pip install requests ddddocr
```

## 📁 项目结构

```
├── login.py          # 登录模块 - 处理系统登录和会话管理
├── query_list.py     # 查询模块 - 告警列表查询和过滤
├── alert_detail.py   # 详情模块 - 告警详细信息获取
├── example.py        # 使用示例 - 完整的使用演示
└── README.md         # 项目文档
```

## 🔧 核心模块

### 1. 登录模块 (login.py)

`QianXinLoginSimulator` 类提供完整的登录功能：

- **自动验证码识别**: 使用ddddocr库自动识别验证码
- **重试机制**: 支持最多5次登录重试
- **会话管理**: 维护登录状态和CSRF令牌
- **API请求**: 统一的API请求方法

### 2. 查询模块 (query_list.py)

`AlertQueryManager` 类提供多种查询功能：

- **基础查询**: `get_alert_list()` - 获取告警列表
- **条件查询**: 
  - `get_high_risk_alerts()` - 高危告警
  - `get_web_attacks()` - Web攻击
  - `get_unprocessed_alerts()` - 未处置告警
  - `get_alerts_by_ip()` - 按IP查询
  - `get_alerts_by_time_range()` - 按时间范围查询
- **统计分析**: `get_alert_statistics()` - 获取统计信息
- **数据导出**: `export_alerts_to_json()` - 导出JSON格式

### 3. 详情模块 (alert_detail.py)

`AlertDetailManager` 类提供详情查询功能：

- **基本信息**: `get_alert_basic_info()` - 获取告警基本信息
- **网络信息**: `get_alert_network_info()` - 获取请求响应详情
- **位置信息**: `get_alert_location_info()` - 获取地理位置信息
- **格式化显示**: `format_alert_detail()` - 友好的格式化输出
- **数据导出**: `export_alert_detail_to_json()` - 导出详情数据

## 📖 使用示例

### 快速开始

```python
from query_list import create_query_manager
from alert_detail import AlertDetailManager

# 配置信息
BASE_URL = "https://*************"
USERNAME = "admin"
PASSWORD = "your_password"

# 创建查询管理器（自动登录）
query_manager = create_query_manager(
    base_url=BASE_URL,
    username=USERNAME,
    password=PASSWORD,
    max_retries=5,
    verbose=True
)

if query_manager:
    # 查询告警列表
    alerts = query_manager.get_alert_list(limit=10)
    print(f"获取到 {len(alerts['items'])} 条告警")
    
    # 查询告警详情
    detail_manager = AlertDetailManager(query_manager.simulator)
    alert_id = alerts['items'][0]['id']
    detail = detail_manager.get_alert_basic_info(alert_id)
    print(f"告警详情: {detail}")
```

### 高级查询

```python
# 查询高危告警
high_risk = query_manager.get_high_risk_alerts(limit=5)

# 查询Web攻击
web_attacks = query_manager.get_web_attacks(limit=5)

# 按时间范围查询（最近1小时）
recent = query_manager.get_alerts_by_time_range(hours_ago=1, limit=10)

# 按IP地址查询
ip_alerts = query_manager.get_alerts_by_ip(source_ip="*************")

# 多条件搜索
custom_alerts = query_manager.search_alerts(
    hazard_level="高危",
    threat_type="跨站脚本攻击（XSS）",
    limit=20
)
```

### 统计分析

```python
# 获取24小时统计
stats = query_manager.get_alert_statistics(hours=24)
print(f"总告警: {stats['total']}")
print(f"高危: {stats['high_risk']}")
print(f"Web攻击: {stats['web_attacks']}")
print(f"未处置: {stats['unprocessed']}")
```

### 数据导出

```python
# 导出告警列表
query_manager.export_alerts_to_json(
    filename="alerts_export.json",
    limit=100
)

# 导出告警详情
detail_manager.export_alert_detail_to_json(
    alert_id="12345",
    filename="alert_detail.json"
)
```

## 🎯 支持的查询字段

### 告警列表查询支持的过滤条件：

- `hazard_level`: 危险级别（危急、高危、中危、低危）
- `host_state`: 攻击结果（成功、企图、失败）
- `threat_type`: 威胁类型
- `sip/dip`: 源IP/目标IP
- `sport/dport`: 源端口/目标端口
- `is_web_attack`: 是否Web攻击
- `status`: 处置状态
- `start_time/end_time`: 时间范围

### 告警详情包含的信息：

- 基本信息：规则名称、IP地址、端口、协议
- 网络信息：HTTP请求头、请求体、响应头、响应体
- 地理位置：源IP和目标IP的地理位置信息
- 命中信息：规则命中的具体位置和内容

## ⚠️ 注意事项

1. **网络连接**: 确保能够访问目标奇安信流量传感器系统
2. **账户权限**: 使用的账户需要有相应的查询权限
3. **验证码识别**: 验证码识别可能不是100%准确，系统会自动重试
4. **请求频率**: 避免过于频繁的请求，以免对系统造成压力
5. **SSL证书**: 代码中禁用了SSL证书验证，生产环境请根据需要调整

## 🔍 运行示例

直接运行示例文件查看完整演示：

```bash
python example.py
```

示例包含：
- 基本登录和查询流程
- 高级查询功能演示
- 数据导出功能演示

## 📝 更新日志

- v1.0.0: 初始版本，支持基本的登录、查询、详情获取功能
- 支持自动验证码识别和登录重试
- 提供多种查询条件和统计功能
- 支持数据导出和格式化显示

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
