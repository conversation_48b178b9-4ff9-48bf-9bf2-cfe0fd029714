#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3
from loguru import logger
import requests
# 防火墙管理
# 需要使用selenium库启动无头浏览器进行自动化操作，通过网页控制防火墙硬件进行ip封禁操作

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 操作流程
# 1.登录Xpath 无验证码
# 2.资源管理按钮 Xpath
# 3.地址按钮
# 4.主机名 01-网安封禁内网地址<a id="id_524"> 点击同<tr>下的img
# 5.输入框 Xpath
# 6.<input name=srcInsBut> 按钮进行添加
# 7.<input class="confirm">

# 定义XPath和其他选择器常量
LOGIN_USERNAME_XPATH = "/html/body/div[2]/table/tbody/tr/td/div/form/table/tbody/tr[1]/td[2]/input"  # 请替换为实际的XPath
LOGIN_PASSWORD_XPATH = "/html/body/div[2]/table/tbody/tr/td/div/form/table/tbody/tr[2]/td[2]/input"  # 请替换为实际的XPath
LOGIN_BUTTON_XPATH = "/html/body/div[2]/table/tbody/tr/td/div/form/table/tbody/tr[3]/td[2]/input"  # 请替换为实际的XPath
RESOURCE_MANAGEMENT_XPATH = "/html/body/div/ul/li[2]/h4"  # 请替换为实际的XPath
ADDRESS_BUTTON_XPATH = "/html/body/div/ul/li[2]/div/a[1]"  # 请替换为实际的XPath
HOST_NAME_XPATH = "/html/body/div[1]/table/tbody/tr/td/div[2]/table/tbody/tr[525]/td[1]/a"  # 请替换为实际的XPath
HOST_IMG_XPATH = "/html/body/div[1]/table/tbody/tr/td/div[2]/table/tbody/tr[525]/td[4]/img"  # 请替换为实际的XPath
INPUT_FIELD_XPATH = "/html/body/div/table/tbody/tr/td/form/table[1]/tbody/tr[3]/td/table/tbody/tr[3]/td[4]/input"  # 请替换为实际的XPath
ADD_BUTTON_XPATH = "/html/body/div/table/tbody/tr/td/form/table[1]/tbody/tr[3]/td/table/tbody/tr[3]/td[3]/input[1]"  # 根据注释
CONFIRM_BUTTON_XPATH = "/html/body/div/table/tbody/tr/td/form/table[2]/tbody/tr[2]/td/input[4]"  # 根据注释

# 防火墙登录凭据
FIREWALL_USERNAME = "wabsuper"
FIREWALL_PASSWORD = "whgD@955989"  # 密码
FIREWALL_URL = "https://*************/"  # URL

async def firewalled(ip):
    """
    使用Selenium操控无头浏览器进行防火墙IP封禁
    
    Args:
        ip: 要封禁的IP地址
        
    Returns:
        dict: 包含操作状态和结果的字典
    """
    logger.info("[防火墙管理] APP执行参数为: {ip}", ip=ip)
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    
    try:
        # 初始化WebDriver
        logger.info("[防火墙管理] 初始化WebDriver")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 设置等待
        wait = WebDriverWait(driver, 10)
        
        # 访问防火墙登录页面
        logger.info("[防火墙管理] 访问防火墙登录页面")
        driver.get(FIREWALL_URL)
        
        # 1. 登录操作
        logger.info("[防火墙管理] 执行登录操作")
        username_input = wait.until(EC.presence_of_element_located((By.XPATH, LOGIN_USERNAME_XPATH)))
        password_input = driver.find_element(By.XPATH, LOGIN_PASSWORD_XPATH)
        
        username_input.send_keys(FIREWALL_USERNAME)
        password_input.send_keys(FIREWALL_PASSWORD)
        
        login_button = driver.find_element(By.XPATH, LOGIN_BUTTON_XPATH)
        login_button.click()
        
        # 等待登录成功
        time.sleep(2)
        
        # 2. 点击资源管理按钮
        logger.info("[防火墙管理] 点击资源管理按钮")
        resource_management = wait.until(EC.element_to_be_clickable((By.XPATH, RESOURCE_MANAGEMENT_XPATH)))
        resource_management.click()
        
        # 3. 点击地址按钮
        logger.info("[防火墙管理] 点击地址按钮")
        address_button = wait.until(EC.element_to_be_clickable((By.XPATH, ADDRESS_BUTTON_XPATH)))
        address_button.click()
        
        # 4. 点击主机名下的img
        logger.info("[防火墙管理] 点击主机名下的img")
        host_img = wait.until(EC.element_to_be_clickable((By.XPATH, HOST_IMG_XPATH)))
        host_img.click()
        
        # 5. 在输入框中输入IP
        logger.info("[防火墙管理] 在输入框中输入IP: {ip}", ip=ip)
        ip_input = wait.until(EC.presence_of_element_located((By.XPATH, INPUT_FIELD_XPATH)))
        ip_input.clear()
        ip_input.send_keys(ip)
        
        # 6. 点击添加按钮
        logger.info("[防火墙管理] 点击添加按钮")
        add_button = wait.until(EC.element_to_be_clickable((By.XPATH, ADD_BUTTON_XPATH)))
        add_button.click()
        
        # 7. 点击确认按钮
        logger.info("[防火墙管理] 点击确认按钮")
        confirm_button = wait.until(EC.element_to_be_clickable((By.XPATH, CONFIRM_BUTTON_XPATH)))
        confirm_button.click()
        
        # 等待操作完成
        time.sleep(2)
        
        logger.info("[防火墙管理] IP封禁操作完成: {ip}", ip=ip)
        return {"status": 0, "result": f"成功封禁IP: {ip}"}
        
    except Exception as e:
        logger.error("[防火墙管理] 操作失败: {e}", e=e)
        return {"status": 2, "result": f"防火墙管理操作失败: {str(e)}"}
        
    finally:
        # 确保浏览器关闭
        if driver:
            logger.info("[防火墙管理] 关闭WebDriver")
            driver.quit()

if __name__ == '__main__':
    import asyncio
    result = asyncio.run(firewalled(ip='*******'))
    print(result)
