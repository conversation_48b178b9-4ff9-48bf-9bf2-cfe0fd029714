# AM自动化应用

## APP 说明

> AM自动化系统接口，支持多种任务类型的自动化执行，包括工作通知、任务、联系单等的自动化处理和发送。

## 功能特性

- 支持5种任务类型：工作通知、工作任务、工作联系单、预警通知、预警通告
- 支持默认收件人和自定义收件人两种模式
- 提供任务状态查询功能
- 异步执行，支持长时间运行的自动化任务

## 动作列表

### 启动任务

启动AM自动化任务，支持选择任务类型和收件人配置。

**参数：**

|  参数   | 类型  |  必填   |  默认值  |  备注  |
|  ----  | ----  |  ----  |  ----  |  ----  |
| **task_type**  | select | `是` | notice | 任务类型 |
| **recipient_mode**  | select | `是` | default | 收件人模式 |
| **custom_recipients**  | textarea | `否` | - | 自定义收件人列表 |

**任务类型说明：**
- `notice`: 工作通知
- `task`: 工作任务
- `contact_sheet`: 工作联系单
- `warning_notice`: 预警单反馈
- `alert_announcement`: 全省预警通告

**收件人模式说明：**
- `default`: 使用默认收件人 (网络安全监控副值)
- `s6000`: 使用S6000预设收件人列表 (郭竞知, 刘冲, 黄梦琦, 网络安全监控主值, 网络安全监控副值)
- `custom`: 使用自定义收件人列表

**收件人代码对照表：**
| 代码 | 姓名 | 代码 | 姓名 | 代码 | 姓名 |
|------|------|------|------|------|------|
| cjl | 陈俊龙 | fzx | 付忠祥 | gjz | 郭竞知 |
| gz | 郭政 | hmq | 黄梦琦 | lc | 刘冲 |
| lw | 李威 | qsh | 覃思航 | tc | 田聪 |
| wc | 魏朝 | yjc | 叶嘉诚 | yk | 杨凯 |
| zzx | 邹子旭 | wlaqdc | 网络安全督查 | wlaqfx | 网络安全分析 |
| wlaqjkfz | 网络安全监控副值 | wlaqjkzz | 网络安全监控主值 | | |

**自定义收件人格式：**
支持逗号分隔或换行分隔，例如：
```
cjl, wlaqjkzz, tc, wlaqjkfz
```
或
```
cjl
wlaqjkzz
tc
wlaqjkfz
```

**返回值：**

```json
{
  "status": 0,
  "result": "任务已成功提交！\n任务类型: 工作通知\n收件人: wlaqjkfz\n服务器响应: 任务已接受\n请使用查询状态功能获取执行结果"
}
```

### 查询状态

查询当前任务的执行状态。

**参数：**

无需参数，直接查询固定服务器的任务状态。

**返回值：**

```json
{
  "status": 0,
  "result": "任务状态: 成功\n任务类型: 工作通知\n状态信息: RPA 任务成功完成，已向 'wlaqjkfz' 发送 3 个项目。\n更新时间: 2025-01-18 10:30:00"
}
```

## 使用示例

### 1. 使用默认收件人启动工作通知任务

```json
{
  "task_type": "notice",
  "recipient_mode": "default"
}
```

### 2. 使用S6000预设收件人启动工作任务

```json
{
  "task_type": "task",
  "recipient_mode": "s6000"
}
```

### 3. 使用自定义收件人启动预警通告任务

```json
{
  "task_type": "alert_announcement",
  "recipient_mode": "custom",
  "custom_recipients": "cjl, wlaqjkzz, tc, wlaqjkfz"
}
```

### 4. 查询任务状态

无需参数，直接调用查询状态功能即可。

## 错误处理

应用会处理以下常见错误情况：
- 网络连接超时
- 服务器无响应
- 任务冲突（系统忙碌）
- 参数验证错误
- 自定义收件人格式错误

## 注意事项

1. 确保目标Windows机器上的AM自动化服务正在运行
2. 网络连接正常，能够访问指定的服务器地址和端口
3. 自定义收件人模式下必须提供有效的收件人列表
4. 任务执行可能需要较长时间，建议使用状态查询功能跟踪进度
