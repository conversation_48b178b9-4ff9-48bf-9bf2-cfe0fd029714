#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天眼告警查询APP测试脚本
"""

import asyncio
import sys
import os

# 添加main目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'main'))

from main.run import query_alerts


async def test_tianyan_alert_app():
    """
    测试天眼告警查询APP
    """
    print("=" * 60)
    print("天眼告警查询APP测试")
    print("=" * 60)
    
    # 测试参数（请根据实际情况修改）
    base_url = "https://10.228.16.244"
    username = "admin"
    password = ""  # 请填入实际密码
    hours_ago = "1"  # 测试字符串类型参数
    limit = "10"     # 测试字符串类型参数
    
    print(f"测试参数:")
    print(f"  平台地址: {base_url}")
    print(f"  用户名: {username}")
    print(f"  查询时间范围: 过去{hours_ago}小时")
    print(f"  返回数量限制: {limit}条")
    print()
    
    if not password:
        print("⚠️  请在脚本中填入正确的密码后再运行测试")
        return
    
    try:
        print("开始测试...")
        result = await query_alerts(
            base_url=base_url,
            username=username,
            password=password,
            hours_ago=hours_ago,
            limit=limit
        )
        
        print("=" * 60)
        print("测试结果:")
        print("=" * 60)
        
        if result["status"] == 0:
            print("✅ 测试成功!")
            data = result["result"]
            print(f"📊 总告警数: {data['total']}")
            
            if data["alerts"]:
                print("\n📋 告警列表:")
                print("-" * 80)
                for i, alert in enumerate(data["alerts"][:5], 1):  # 只显示前5条
                    print(f"{i}. 监测时间: {alert['access_time']}")
                    print(f"   受害者IP: {alert['alarm_sip']}")
                    print(f"   告警类型: {alert['table_name']}")
                    print(f"   威胁名称: {alert['rule_name']}")
                    print()
                
                if data['total'] > 5:
                    print(f"... 还有 {data['total'] - 5} 条告警")
            else:
                print("📭 指定时间范围内没有告警")
        else:
            print("❌ 测试失败!")
            print(f"错误信息: {result['result']}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(test_tianyan_alert_app())
