#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器告警详情查询模块
根据告警ID查询详细信息，包括数据包信息、请求响应详情等
"""

import random
import json
from login import QianXinLoginSimulator


class AlertDetailManager:
    def __init__(self, login_simulator):
        """
        初始化告警详情管理器
        
        Args:
            login_simulator (QianXinLoginSimulator): 已登录的模拟器实例
        """
        self.simulator = login_simulator
    
    def get_alert_packet_info(self, alert_id, alert_type="webids_alert"):
        """
        获取告警的数据包详细信息
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型，默认为 "webids_alert"
            
        Returns:
            dict: 告警数据包详情，如果请求失败返回None
        """
        # 构建查询参数
        params = {
            'alert_id': str(alert_id),
            'type': alert_type,
            'csrf_token': self.simulator.csrf_token,
            'r': random.random()
        }
        
        try:
            response = self.simulator.api_request('GET', '/skyeye/alarm/info/packet', params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"获取告警详情失败，状态码: {response.status_code}")
                if response.status_code == 404:
                    print(f"告警ID {alert_id} 不存在或已被删除")
                elif response.status_code == 403:
                    print("权限不足，无法访问告警详情")
                return None
                
        except Exception as e:
            print(f"获取告警详情异常: {e}")
            return None
    
    def get_alert_basic_info(self, alert_id, alert_type="webids_alert"):
        """
        获取告警基本信息（从数据包信息中提取关键字段）
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型
            
        Returns:
            dict: 告警基本信息
        """
        packet_info = self.get_alert_packet_info(alert_id, alert_type)
        
        if not packet_info or packet_info.get('status') != 200:
            return None
        
        items = packet_info.get('items', {})
        if not items:
            return None
        
        # 提取基本信息
        basic_info = {
            'alert_id': alert_id,
            'source_ip': items.get('sip', 'Unknown'),
            'source_port': items.get('sport', 'Unknown'),
            'dest_ip': items.get('dip', 'Unknown'),
            'dest_port': items.get('dport', 'Unknown'),
            'protocol': items.get('proto', 'Unknown'),
            'rule_name': items.get('rule_name', 'Unknown'),
            'username': items.get('username', 'Unknown'),
            'is_weak_password': items.get('is_weak_passwd', 0),
            'vlan_id': items.get('vlan_id', ''),
            'vxlan_id': items.get('vxlan_id', ''),
            'file_info': items.get('file', ''),
            'hit_field': items.get('hit_field', ''),
            'hit_start': items.get('hit_start', ''),
            'hit_end': items.get('hit_end', '')
        }
        
        return basic_info
    
    def get_alert_network_info(self, alert_id, alert_type="webids_alert"):
        """
        获取告警网络信息（请求和响应详情）
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型
            
        Returns:
            dict: 网络请求响应信息
        """
        packet_info = self.get_alert_packet_info(alert_id, alert_type)
        
        if not packet_info or packet_info.get('status') != 200:
            return None
        
        items = packet_info.get('items', {})
        if not items:
            return None
        
        # 提取网络信息
        network_info = {
            'alert_id': alert_id,
            'request_header': items.get('req_header', ''),
            'request_body': items.get('req_body', ''),
            'response_header': items.get('rsp_header', ''),
            'response_body': items.get('rsp_body', ''),
            'packet_data': items.get('packet_data', ''),
            'packet_size': items.get('packet_size', ''),
            'protocol_info': items.get('protocol', '')
        }
        
        return network_info
    
    def get_alert_location_info(self, alert_id, alert_type="webids_alert"):
        """
        获取告警地理位置信息
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型
            
        Returns:
            dict: 地理位置信息
        """
        packet_info = self.get_alert_packet_info(alert_id, alert_type)
        
        if not packet_info or packet_info.get('status') != 200:
            return None
        
        items = packet_info.get('items', {})
        if not items:
            return None
        
        # 提取地理位置信息
        location_info = {
            'alert_id': alert_id,
            'source_location': items.get('loc_sip', {}),
            'dest_location': items.get('loc_dip', {})
        }
        
        return location_info
    
    def format_alert_detail(self, alert_id, alert_type="webids_alert", show_full_content=False):
        """
        格式化显示告警详情
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型
            show_full_content (bool): 是否显示完整的请求响应内容
            
        Returns:
            str: 格式化的告警详情字符串
        """
        packet_info = self.get_alert_packet_info(alert_id, alert_type)
        
        if not packet_info:
            return f"❌ 无法获取告警 {alert_id} 的详情"
        
        if packet_info.get('status') != 200:
            return f"❌ 告警 {alert_id} 查询失败: {packet_info.get('message', 'Unknown error')}"
        
        items = packet_info.get('items', {})
        if not items:
            return f"❌ 告警 {alert_id} 没有详细信息"
        
        # 构建格式化输出
        output = []
        output.append(f"📋 告警详情 - ID: {alert_id}")
        output.append("=" * 60)
        
        # 基本信息
        output.append("🔍 基本信息:")
        output.append(f"  规则名称: {items.get('rule_name', 'Unknown')}")
        output.append(f"  源IP: {items.get('sip', 'Unknown')}:{items.get('sport', 'Unknown')}")
        output.append(f"  目标IP: {items.get('dip', 'Unknown')}:{items.get('dport', 'Unknown')}")
        output.append(f"  协议: {items.get('proto', 'Unknown')}")
        
        if items.get('is_weak_passwd'):
            output.append(f"  ⚠️  弱密码检测: 是")
        
        # 地理位置信息
        loc_sip = items.get('loc_sip', {})
        loc_dip = items.get('loc_dip', {})
        
        if loc_sip or loc_dip:
            output.append("\n🌍 地理位置信息:")
            if loc_sip:
                output.append(f"  源IP位置: {loc_sip.get('location', 'Unknown')} ({loc_sip.get('country', 'Unknown')})")
                if loc_sip.get('isp'):
                    output.append(f"  源IP ISP: {loc_sip.get('isp')}")
            
            if loc_dip:
                output.append(f"  目标IP位置: {loc_dip.get('location', 'Unknown')} ({loc_dip.get('country', 'Unknown')})")
                if loc_dip.get('isp'):
                    output.append(f"  目标IP ISP: {loc_dip.get('isp')}")
        
        # 网络信息
        if items.get('req_header') or items.get('req_body'):
            output.append("\n📤 请求信息:")
            if items.get('req_header'):
                if show_full_content:
                    output.append(f"  请求头:\n{items.get('req_header')}")
                else:
                    header_lines = items.get('req_header', '').split('\r\n')[:3]
                    output.append(f"  请求头: {' | '.join(header_lines)} ...")
            
            if items.get('req_body'):
                if show_full_content:
                    output.append(f"  请求体:\n{items.get('req_body')}")
                else:
                    body = items.get('req_body', '')
                    if len(body) > 100:
                        body = body[:100] + "..."
                    output.append(f"  请求体: {body}")
        
        if items.get('rsp_header') or items.get('rsp_body'):
            output.append("\n📥 响应信息:")
            if items.get('rsp_header'):
                if show_full_content:
                    output.append(f"  响应头:\n{items.get('rsp_header')}")
                else:
                    header_lines = items.get('rsp_header', '').split('\r\n')[:3]
                    output.append(f"  响应头: {' | '.join(header_lines)} ...")
            
            if items.get('rsp_body'):
                if show_full_content:
                    output.append(f"  响应体:\n{items.get('rsp_body')}")
                else:
                    body = items.get('rsp_body', '')
                    if len(body) > 100:
                        body = body[:100] + "..."
                    output.append(f"  响应体: {body}")
        
        # 命中信息
        if items.get('hit_field'):
            output.append("\n🎯 命中信息:")
            output.append(f"  命中字段: {items.get('hit_field')}")
            if items.get('hit_start') is not None and items.get('hit_end') is not None:
                output.append(f"  命中位置: {items.get('hit_start')} - {items.get('hit_end')}")
        
        return "\n".join(output)
    
    def export_alert_detail_to_json(self, alert_id, alert_type="webids_alert", filename=None):
        """
        导出告警详情到JSON文件
        
        Args:
            alert_id (str/int): 告警ID
            alert_type (str): 告警类型
            filename (str): 文件名，默认自动生成
            
        Returns:
            str: 导出的文件名，失败返回None
        """
        packet_info = self.get_alert_packet_info(alert_id, alert_type)
        
        if not packet_info:
            print(f"❌ 无法获取告警 {alert_id} 的详情，导出失败")
            return None
        
        if filename is None:
            filename = f"alert_detail_{alert_id}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(packet_info, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 告警 {alert_id} 详情已导出到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 导出告警详情失败: {e}")
            return None


def create_alert_detail_manager(base_url, username, password, max_retries=5, verbose=True):
    """
    创建告警详情管理器的便捷函数，支持自动重试

    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码
        max_retries (int): 最大重试次数，默认5次
        verbose (bool): 是否显示详细日志，默认True

    Returns:
        AlertDetailManager: 告警详情管理器实例，如果登录失败返回None
    """
    # 创建登录模拟器
    simulator = QianXinLoginSimulator(base_url)

    # 执行登录（带重试）
    if simulator.login(username, password, auto_recognize=True, verbose=verbose, max_retries=max_retries):
        if verbose:
            print("登录成功，创建告警详情管理器")
        return AlertDetailManager(simulator)
    else:
        if verbose:
            print("登录失败，无法创建告警详情管理器")
        return None
