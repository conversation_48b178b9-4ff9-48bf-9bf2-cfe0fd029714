#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

import requests
import json
from loguru import logger
import time


# 服务器配置
SERVER_HOST = "*************"
SERVER_PORT = 6000

# 默认收件人配置
DEFAULT_RECIPIENTS = ["wlaqjkfz"]

# S6000收件人配置
S6000_RECIPIENTS = ["gjz", "lc", "hmq", "wlaqjkzz", "wlaqjkfz"]

# 任务类型说明
TASK_TYPE_DESCRIPTIONS = {
    "notice": "工作通知",
    "task": "工作任务",
    "contact_sheet": "工作联系单",
    "warning_notice": "预警单反馈",
    "alert_announcement": "全省预警通告"
}

# 收件人中英文对应字典
RECIPIENT_NAMES = {
    "cjl": "陈俊龙",
    "fzx": "付忠祥",
    "gjz": "郭竞知",
    "gz": "郭政",
    "hmq": "黄梦琦",
    "lc": "刘冲",
    "lw": "李威",
    "qsh": "覃思航",
    "tc": "田聪",
    "wc": "魏朝",
    "wlaqdc": "网络安全督查",
    "wlaqfx": "网络安全分析",
    "wlaqjkfz": "网络安全监控副值",
    "wlaqjkzz": "网络安全监控主值",
    "yjc": "叶嘉诚",
    "yk": "杨凯",
    "zzx": "邹子旭"
}


async def start_task(task_type, recipient_mode, custom_recipients=None):
    """
    启动AM自动化任务

    Args:
        task_type: 任务类型
        recipient_mode: 收件人模式 (default/custom)
        custom_recipients: 自定义收件人列表 (当recipient_mode为custom时使用)
    """
    logger.info("[AM_AUTO] 启动任务 - 参数: task_type={}, recipient_mode={}",
                task_type, recipient_mode)
    
    try:
        # 构建API URL
        api_url = f"http://{SERVER_HOST}:{SERVER_PORT}/start-task"
        
        # 处理收件人列表
        if recipient_mode == "default":
            recipients = DEFAULT_RECIPIENTS
        elif recipient_mode == "s6000":
            recipients = S6000_RECIPIENTS
        elif recipient_mode == "custom":
            if not custom_recipients:
                return {"status": 1, "result": "自定义模式下必须提供收件人列表"}

            # 解析自定义收件人 (支持逗号分隔或换行分隔)
            if isinstance(custom_recipients, str):
                recipients = [r.strip() for r in custom_recipients.replace('\n', ',').split(',') if r.strip()]
            else:
                recipients = custom_recipients
        else:
            return {"status": 1, "result": f"不支持的收件人模式: {recipient_mode}"}
        
        # 构建请求数据
        request_data = {
            "task_type": task_type,
            "recipient": recipients
        }
        
        logger.info("[AM_AUTO] 发送请求到: {}, 数据: {}", api_url, request_data)
        
        # 发送POST请求
        response = requests.post(
            api_url,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        # 检查响应状态
        if response.status_code == 202:
            # 任务已接受
            result_data = response.json()
            task_description = TASK_TYPE_DESCRIPTIONS.get(task_type, task_type)

            # 格式化收件人显示（显示中文名称）
            recipient_display = []
            for recipient in recipients:
                chinese_name = RECIPIENT_NAMES.get(recipient, recipient)
                recipient_display.append(f"{chinese_name}({recipient})")

            return {
                "status": 0,
                "result": f"任务已成功提交！\n"
                         f"任务类型: {task_description}\n"
                         f"收件人: {', '.join(recipient_display)}\n"
                         f"服务器响应: {result_data.get('message', '任务已接受')}\n"
                         f"请使用查询状态功能获取执行结果"
            }
        elif response.status_code == 409:
            # 系统忙碌
            error_data = response.json()
            return {
                "status": 1,
                "result": f"任务提交失败: {error_data.get('message', '系统正忙，已有任务在运行中')}"
            }
        else:
            # 其他错误
            try:
                error_data = response.json()
                error_message = error_data.get('message', f'HTTP {response.status_code}')
            except:
                error_message = f'HTTP {response.status_code}: {response.text}'
            
            return {
                "status": 1,
                "result": f"任务提交失败: {error_message}"
            }
            
    except requests.exceptions.Timeout:
        logger.error("[AM_AUTO] 请求超时")
        return {"status": 1, "result": "请求超时，请检查网络连接或服务器状态"}
    
    except requests.exceptions.ConnectionError:
        logger.error("[AM_AUTO] 连接错误")
        return {"status": 1, "result": f"无法连接到服务器 {SERVER_HOST}:{SERVER_PORT}，请检查服务器是否运行"}
    
    except Exception as e:
        logger.error("[AM_AUTO] 执行出错: {}", str(e))
        return {"status": 1, "result": f"执行出错: {str(e)}"}


async def get_status():
    """
    查询AM自动化任务状态
    """
    logger.info("[AM_AUTO] 查询任务状态")
    
    try:
        # 构建API URL
        api_url = f"http://{SERVER_HOST}:{SERVER_PORT}/task-status"
        
        logger.info("[AM_AUTO] 发送状态查询请求到: {}", api_url)
        
        # 发送GET请求
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            status_data = response.json()
            
            # 格式化状态信息
            status = status_data.get('status', 'unknown')
            task_type = status_data.get('task_type', '无')
            message = status_data.get('message', '无信息')
            timestamp = status_data.get('timestamp', '无')
            
            # 状态映射
            status_map = {
                'idle': '空闲',
                'running': '运行中',
                'success': '成功',
                'error': '错误'
            }
            
            status_text = status_map.get(status, status)
            task_description = TASK_TYPE_DESCRIPTIONS.get(task_type, task_type) if task_type != '无' else '无'
            
            result_text = f"任务状态: {status_text}\n"
            result_text += f"任务类型: {task_description}\n"
            result_text += f"状态信息: {message}\n"
            result_text += f"更新时间: {timestamp}"
            
            return {"status": 0, "result": result_text}
        else:
            try:
                error_data = response.json()
                error_message = error_data.get('message', f'HTTP {response.status_code}')
            except:
                error_message = f'HTTP {response.status_code}: {response.text}'
            
            return {"status": 1, "result": f"状态查询失败: {error_message}"}
            
    except requests.exceptions.Timeout:
        logger.error("[AM_AUTO] 状态查询超时")
        return {"status": 1, "result": "状态查询超时，请检查网络连接"}
    
    except requests.exceptions.ConnectionError:
        logger.error("[AM_AUTO] 连接错误")
        return {"status": 1, "result": f"无法连接到服务器 {SERVER_HOST}:{SERVER_PORT}"}
    
    except Exception as e:
        logger.error("[AM_AUTO] 状态查询出错: {}", str(e))
        return {"status": 1, "result": f"状态查询出错: {str(e)}"}
